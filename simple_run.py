#!/usr/bin/env python3
"""
تشغيل مبسط لتطبيق تسجيل الغياب
Simple run script for attendance tracking application
"""

import sys
import os
from pathlib import Path

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    """الدالة الرئيسية"""
    print("=== نظام تسجيل غياب الطلاب ===")
    print("مدرسة أبو عبيدة المتوسطة")
    print("=" * 40)
    
    try:
        # إنشاء المجلدات المطلوبة
        directories = ["data", "config", "logs", "reports", "backups"]
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # تشغيل التطبيق
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        
        window = MainWindow()
        window.show()
        
        print("✓ تم تشغيل التطبيق بنجاح")
        return app.exec()
        
    except Exception as e:
        print(f"✗ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
