# 📖 دليل المستخدم - نظام تسجيل غياب الطلاب

## 🚀 البدء السريع

### تشغيل البرنامج
```bash
# التشغيل الرسمي مع شاشة البداية
python3 main_app.py

# التشغيل المبسط للاختبار
python3 test_gui.py
```

## 🖥️ الواجهة الرئيسية

### 1. تسجيل الغياب اليومي
- **اختيار التاريخ**: استخدم منتقي التاريخ في أعلى الشاشة
- **تحديد الطلاب الغائبين**: ضع علامة ✓ أمام أسماء الطلاب الغائبين
- **نوع الغياب**: اختر من القائمة المنسدلة (غياب، غياب بعذر، تأخير، انصراف مبكر)
- **السبب**: أدخل سبب الغياب (اختياري)
- **الحفظ**: اضغط زر "حفظ الغياب" لحفظ البيانات

### 2. الإحصائيات الفورية
- **إجمالي الطلاب**: العدد الكلي للطلاب المسجلين
- **الحاضرون**: عدد الطلاب الحاضرين اليوم
- **الغائبون**: عدد الطلاب الغائبين اليوم
- **نسبة الغياب**: النسبة المئوية للغياب

### 3. أدوات التحكم السريع
- **تحديد الكل**: تحديد جميع الطلاب كغائبين
- **إلغاء الكل**: إلغاء تحديد جميع الطلاب
- **تحديث**: إعادة تحميل البيانات
- **التقارير**: فتح نافذة التقارير
- **الإعدادات**: فتح نافذة الإعدادات

## 📊 نافذة التقارير

### 1. تبويب إنشاء التقارير
#### اختيار نوع التقرير:
- **تقرير يومي**: غياب يوم محدد
- **تقرير أسبوعي**: غياب أسبوع كامل
- **تقرير شهري**: غياب شهر كامل
- **تقرير مخصص**: فترة زمنية محددة

#### تحديد الفترة الزمنية:
- **من تاريخ**: تاريخ البداية
- **إلى تاريخ**: تاريخ النهاية
- **التاريخ الهجري**: يظهر تلقائياً

#### المرشحات:
- **الصف**: اختر صف محدد أو جميع الصفوف
- **نوع الغياب**: اختر نوع محدد أو جميع الأنواع

#### إنشاء التقرير:
1. اختر نوع التقرير
2. حدد الفترة الزمنية
3. اختر المرشحات المطلوبة
4. اضغط "إنشاء التقرير"

### 2. تبويب معاينة التقرير
- **معاينة HTML**: عرض التقرير بتنسيق جميل
- **الإحصائيات**: ملخص سريع للبيانات
- **تصدير Word**: حفظ التقرير كملف Word

### 3. تبويب الإحصائيات
- **إحصائيات عامة**: أرقام شاملة للمدرسة
- **إحصائيات الصفوف**: تفصيل حسب كل صف
- **الاتجاهات**: تحليل أنماط الغياب

## ⚙️ نافذة الإعدادات

### 1. تبويب الإعدادات العامة
#### إعدادات اللغة والعرض:
- **اللغة**: العربية (افتراضي)
- **الخط**: Tahoma أو Arial
- **حجم الخط**: قابل للتعديل
- **اتجاه النص**: من اليمين لليسار

#### إعدادات التنبيهات:
- **تنبيهات الحفظ**: تأكيد عند حفظ البيانات
- **تنبيهات الأخطاء**: عرض رسائل الخطأ
- **تنبيهات النسخ الاحتياطي**: تذكير بالنسخ الاحتياطي

### 2. تبويب إدارة البيانات
#### استيراد البيانات:
1. اضغط "تحديد ملف Excel"
2. اختر ملف Excel يحتوي على بيانات الطلاب
3. اضغط "استيراد البيانات"
4. تأكد من نجاح العملية

#### تصدير البيانات:
1. اضغط "تصدير إلى Excel"
2. اختر مجلد الحفظ
3. انتظر انتهاء العملية

#### إحصائيات قاعدة البيانات:
- **عدد الطلاب**: إجمالي الطلاب المسجلين
- **عدد سجلات الغياب**: إجمالي سجلات الغياب
- **حجم قاعدة البيانات**: المساحة المستخدمة

#### صيانة قاعدة البيانات:
- **تحسين القاعدة**: تحسين الأداء
- **فحص التكامل**: التأكد من سلامة البيانات
- **إعادة الفهرسة**: تحديث الفهارس

### 3. تبويب النسخ الاحتياطي
#### النسخ الاحتياطي التلقائي:
- **تفعيل النسخ التلقائي**: تشغيل/إيقاف
- **تكرار النسخ**: يومي، أسبوعي، شهري
- **عدد النسخ المحفوظة**: الحد الأقصى للنسخ

#### النسخ الاحتياطي اليدوي:
1. اضغط "إنشاء نسخة احتياطية"
2. أدخل وصف للنسخة (اختياري)
3. انتظر انتهاء العملية

#### استعادة النسخ الاحتياطية:
1. اختر النسخة من القائمة
2. اضغط "استعادة"
3. أكد العملية

### 4. تبويب معلومات المدرسة
#### البيانات الأساسية:
- **اسم المدرسة**: مدرسة أبو عبيدة المتوسطة
- **رقم المدرسة**: الرقم الرسمي
- **المنطقة التعليمية**: المنطقة التابعة لها
- **العنوان**: العنوان الكامل

#### بيانات الاتصال:
- **الهاتف**: رقم هاتف المدرسة
- **الفاكس**: رقم الفاكس
- **البريد الإلكتروني**: البريد الرسمي
- **الموقع الإلكتروني**: رابط الموقع

#### المسؤولون:
- **مدير المدرسة**: الاسم والمنصب
- **وكيل المدرسة**: الاسم والمنصب
- **المرشد الطلابي**: الاسم والمنصب

## 🔧 نصائح الاستخدام

### للحصول على أفضل أداء:
1. **أغلق البرامج الأخرى** عند تشغيل التطبيق
2. **احفظ البيانات بانتظام** باستخدام زر الحفظ
3. **أنشئ نسخ احتياطية دورية** لحماية البيانات
4. **حدث البيانات يومياً** لضمان الدقة

### لحل المشاكل الشائعة:
- **البرنامج لا يفتح**: تأكد من تثبيت Python و PySide6
- **البيانات لا تحفظ**: تحقق من صلاحيات الكتابة في المجلد
- **التقارير لا تصدر**: تأكد من تثبيت python-docx
- **الخطوط لا تظهر**: تأكد من وجود خطوط Tahoma أو Arial

## 📞 الدعم الفني

للحصول على المساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXXXXXX
- **ساعات العمل**: الأحد - الخميس، 7:00 ص - 3:00 م

---

**إصدار الدليل**: 1.0.0  
**تاريخ التحديث**: 2024-06-24  
**متوافق مع**: نظام تسجيل غياب الطلاب v1.0.0
