#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد المشروع - نظام تسجيل غياب الطلاب
Project Setup - Student Attendance System

مدرسة أبو عبيدة المتوسطة
Abu Ubaida Intermediate School
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "نظام تسجيل غياب الطلاب - مدرسة أبو عبيدة المتوسطة"

# قراءة متطلبات المشروع
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
    return requirements

setup(
    # معلومات المشروع الأساسية
    name="student-attendance-system",
    version="1.0.0",
    description="نظام تسجيل غياب الطلاب - مدرسة أبو عبيدة المتوسطة",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    
    # معلومات المطور
    author="فريق تطوير الأنظمة التعليمية",
    author_email="<EMAIL>",
    url="https://github.com/school/attendance-system",
    
    # تصنيف المشروع
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Education",
        "Topic :: Education :: Computer Aided Instruction (CAI)",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
    ],
    
    # الكلمات المفتاحية
    keywords="attendance school education arabic students management",
    
    # الحزم والملفات
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    include_package_data=True,
    package_data={
        "": ["*.json", "*.sql", "*.txt", "*.md"],
        "ui": ["*.ui", "*.qrc"],
        "data": ["*.db", "*.xls", "*.xlsx"],
        "config": ["*.json"],
        "templates": ["*.docx", "*.html"],
    },
    
    # المتطلبات
    python_requires=">=3.8",
    install_requires=read_requirements(),
    
    # التبعيات الاختيارية
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-qt>=4.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "docs": [
            "sphinx>=4.0",
            "sphinx-rtd-theme>=0.5",
        ],
        "charts": [
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
        ],
        "pdf": [
            "reportlab>=3.6.0",
        ],
    },
    
    # نقاط الدخول
    entry_points={
        "console_scripts": [
            "attendance-system=main_app:main",
            "attendance-test=test_gui:main",
        ],
        "gui_scripts": [
            "attendance-gui=main_app:main",
        ],
    },
    
    # ملفات البيانات
    data_files=[
        ("config", ["config/settings.json"]),
        ("data", ["data/StudentGuidance.xls"]),
        ("docs", ["README.md", "USER_GUIDE.md"]),
        ("scripts", ["run.py", "start.py", "run.bat"]),
    ],
    
    # إعدادات إضافية
    zip_safe=False,
    platforms=["any"],
    license="MIT",
    
    # معلومات المشروع
    project_urls={
        "Bug Reports": "https://github.com/school/attendance-system/issues",
        "Source": "https://github.com/school/attendance-system",
        "Documentation": "https://github.com/school/attendance-system/wiki",
    },
)
