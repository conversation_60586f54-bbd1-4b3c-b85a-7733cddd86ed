# هيكل المشروع الاحترافي - برنامج تسجيل غياب الطلاب

## 📁 هيكل المجلدات والملفات

```
student_attendance_system/
│
├── 📁 src/                          # المجلد الرئيسي للكود المصدري
│   ├── 📁 models/                   # طبقة النماذج (Models Layer)
│   │   ├── __init__.py
│   │   ├── student.py               # نموذج الطالب
│   │   ├── absence_record.py        # نموذج سجل الغياب
│   │   ├── school_info.py           # نموذج معلومات المدرسة
│   │   ├── grade.py                 # نموذج الصف
│   │   ├── report_template.py       # نموذج قالب التقرير
│   │   ├── user.py                  # نموذج المستخدم
│   │   └── base_model.py            # النموذج الأساسي
│   │
│   ├── 📁 data/                     # طبقة الوصول للبيانات (DAL)
│   │   ├── __init__.py
│   │   ├── database_manager.py      # مدير قاعدة البيانات
│   │   ├── repositories/            # مستودعات البيانات
│   │   │   ├── __init__.py
│   │   │   ├── student_repository.py
│   │   │   ├── absence_repository.py
│   │   │   ├── report_repository.py
│   │   │   └── base_repository.py
│   │   └── migrations/              # ملفات ترحيل قاعدة البيانات
│   │       ├── __init__.py
│   │       ├── 001_create_tables.sql
│   │       ├── 002_add_indexes.sql
│   │       └── 003_insert_default_data.sql
│   │
│   ├── 📁 services/                 # طبقة منطق الأعمال (BLL)
│   │   ├── __init__.py
│   │   ├── absence_service.py       # خدمة إدارة الغياب
│   │   ├── report_service.py        # خدمة التقارير
│   │   ├── excel_import_service.py  # خدمة استيراد Excel
│   │   ├── hijri_date_service.py    # خدمة التاريخ الهجري
│   │   ├── word_export_service.py   # خدمة تصدير Word
│   │   └── validation_service.py    # خدمة التحقق من البيانات
│   │
│   ├── 📁 ui/                       # طبقة واجهة المستخدم (UI Layer)
│   │   ├── __init__.py
│   │   ├── main_window.py           # النافذة الرئيسية
│   │   ├── windows/                 # النوافذ الفرعية
│   │   │   ├── __init__.py
│   │   │   ├── absence_window.py    # نافذة تسجيل الغياب
│   │   │   ├── reports_window.py    # نافذة التقارير
│   │   │   ├── designer_window.py   # نافذة مصمم التقارير
│   │   │   ├── settings_window.py   # نافذة الإعدادات
│   │   │   └── preview_window.py    # نافذة المعاينة
│   │   ├── widgets/                 # العناصر المخصصة
│   │   │   ├── __init__.py
│   │   │   ├── student_selector.py  # عنصر اختيار الطالب
│   │   │   ├── date_picker.py       # عنصر اختيار التاريخ
│   │   │   ├── report_preview.py    # عنصر معاينة التقرير
│   │   │   └── template_designer.py # عنصر مصمم القوالب
│   │   └── resources/               # الموارد
│   │       ├── __init__.py
│   │       ├── styles.qss           # ملف الأنماط
│   │       ├── icons/               # الأيقونات
│   │       └── ui_files/            # ملفات Qt Designer
│   │
│   ├── 📁 utils/                    # الأدوات المساعدة
│   │   ├── __init__.py
│   │   ├── constants.py             # الثوابت
│   │   ├── helpers.py               # الدوال المساعدة
│   │   ├── logger.py                # نظام السجلات
│   │   ├── config.py                # إعدادات التطبيق
│   │   └── exceptions.py            # الاستثناءات المخصصة
│   │
│   └── main.py                      # نقطة دخول التطبيق
│
├── 📁 data/                         # ملفات البيانات
│   ├── database.db                  # قاعدة البيانات الرئيسية
│   ├── StudentGuidance.xls          # ملف بيانات الطلاب الموجود
│   ├── backups/                     # النسخ الاحتياطية
│   └── exports/                     # الملفات المصدرة
│
├── 📁 tests/                        # اختبارات الوحدة
│   ├── __init__.py
│   ├── test_models/
│   ├── test_services/
│   ├── test_repositories/
│   └── test_ui/
│
├── 📁 docs/                         # الوثائق
│   ├── README.md
│   ├── user_manual.md               # دليل المستخدم
│   ├── technical_docs.md            # الوثائق التقنية
│   └── api_reference.md             # مرجع API
│
├── 📁 requirements/                 # متطلبات المشروع
│   ├── base.txt                     # المتطلبات الأساسية
│   ├── dev.txt                      # متطلبات التطوير
│   └── prod.txt                     # متطلبات الإنتاج
│
├── 📁 scripts/                      # سكريبتات مساعدة
│   ├── setup.py                     # سكريبت الإعداد
│   ├── build.py                     # سكريبت البناء
│   └── deploy.py                    # سكريبت النشر
│
├── 📁 config/                       # ملفات الإعدادات
│   ├── settings.json                # إعدادات التطبيق
│   ├── database.json               # إعدادات قاعدة البيانات
│   └── logging.json                # إعدادات السجلات
│
├── .gitignore                       # ملف Git ignore
├── requirements.txt                 # متطلبات Python
├── setup.py                         # ملف الإعداد
├── README.md                        # ملف README
└── LICENSE                          # رخصة المشروع
```

## 🔧 المتطلبات التقنية

### المكتبات الأساسية:
- **PySide6**: واجهة المستخدم الرسومية
- **SQLite3**: قاعدة البيانات
- **pandas**: معالجة ملفات Excel
- **python-docx**: إنشاء ملفات Word
- **hijri-converter**: تحويل التواريخ الهجرية
- **openpyxl**: قراءة ملفات Excel
- **Pillow**: معالجة الصور

### مكتبات التطوير:
- **pytest**: اختبارات الوحدة
- **black**: تنسيق الكود
- **flake8**: فحص جودة الكود
- **mypy**: فحص الأنواع

## 📊 البيانات الموجودة

### معلومات المدرسة:
- **اسم المدرسة**: متوسطة أبي عبيدة
- **المرحلة**: متوسط (رقم 2)

### إحصائيات الطلاب:
- **إجمالي الطلاب**: 147 طالب
- **الصف الأول متوسط (0725)**: 38 طالب
- **الصف الثاني متوسط (0825)**: 55 طالب  
- **الصف الثالث متوسط (0925)**: 54 طالب

### هيكل بيانات الطلاب:
- رقم الهوية المدنية
- الاسم الكامل
- رقم الجوال
- رمز الصف
- رقم الفصل

## 🎯 الميزات الرئيسية

### 1. تسجيل الغياب:
- اختيار الطالب من قائمة
- تعبئة التاريخ الهجري تلقائياً
- تسجيل الغياب بعذر/بدون عذر
- إدخال سبب الغياب
- حفظ البيانات في قاعدة البيانات

### 2. التقارير:
- تقرير طالب محدد
- تقرير صف محدد
- تقرير يومي
- تقرير شهري
- تقرير فصلي
- معاينة التقارير
- تصدير إلى Word

### 3. مصمم التقارير:
- تصميم الهيدر والفوتر
- اختيار الخطوط والألوان
- إضافة الشعار
- حفظ القوالب المخصصة

### 4. الإعدادات:
- استيراد بيانات الطلاب من Excel
- تحديث معلومات المدرسة
- إنشاء نسخ احتياطية
- استعادة البيانات

## 🏗️ المعمارية

### طبقة النماذج (Models):
- تمثيل البيانات
- التحقق من صحة البيانات
- العلاقات بين الكائنات

### طبقة الوصول للبيانات (DAL):
- إدارة الاتصال بقاعدة البيانات
- عمليات CRUD
- استعلامات معقدة

### طبقة منطق الأعمال (BLL):
- قواعد العمل
- معالجة البيانات
- التحقق من صحة العمليات

### طبقة واجهة المستخدم (UI):
- النوافذ والعناصر
- التفاعل مع المستخدم
- عرض البيانات

## 🔄 تدفق العمل

1. **بدء التطبيق**: تحميل قاعدة البيانات والإعدادات
2. **تسجيل الغياب**: اختيار الطالب وتسجيل الغياب
3. **إنشاء التقارير**: اختيار نوع التقرير وتصديره
4. **إدارة البيانات**: استيراد وتصدير البيانات
5. **النسخ الاحتياطي**: حفظ واستعادة البيانات
