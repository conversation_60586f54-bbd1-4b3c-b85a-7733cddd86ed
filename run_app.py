#!/usr/bin/env python3
"""
تشغيل تطبيق تسجيل الغياب
Run attendance tracking application
"""

import sys
import os
from pathlib import Path

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def check_dependencies():
    """التحقق من المتطلبات"""
    required_packages = [
        'PySide6',
        'pandas',
        'python-docx',
        'hijri-converter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python-docx':
                import docx
            elif package == 'hijri-converter':
                import hijri_converter
            elif package == 'PySide6':
                import PySide6
            else:
                __import__(package.lower().replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("المتطلبات المفقودة:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nلتثبيت المتطلبات، استخدم:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """إعداد البيئة"""
    # إنشاء المجلدات المطلوبة
    directories = [
        "data",
        "config", 
        "logs",
        "reports",
        "backups"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
    
    # إنشاء ملف الإعدادات إذا لم يكن موجوداً
    config_file = project_root / "config" / "settings.json"
    if not config_file.exists():
        import json
        
        default_config = {
            "application": {
                "name": "نظام تسجيل غياب الطلاب",
                "version": "1.0.0",
                "author": "مدرسة أبو عبيدة المتوسطة",
                "language": "ar",
                "direction": "rtl"
            },
            "database": {
                "path": "data/attendance.db",
                "backup_path": "backups",
                "auto_backup": True,
                "backup_interval_hours": 24,
                "max_backups": 30,
                "timeout": 30
            },
            "ui": {
                "theme": "default",
                "font_family": "Tahoma",
                "font_size": 12,
                "window_width": 1200,
                "window_height": 800
            },
            "reports": {
                "output_path": "reports",
                "template_path": "templates",
                "default_format": "docx",
                "include_hijri_date": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/app.log",
                "max_file_size": 10485760,
                "backup_count": 5
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print(f"✓ تم إنشاء ملف الإعدادات: {config_file}")

def main():
    """الدالة الرئيسية"""
    print("=== نظام تسجيل غياب الطلاب ===")
    print("مدرسة أبو عبيدة المتوسطة")
    print("=" * 40)
    
    # التحقق من المتطلبات
    print("التحقق من المتطلبات...")
    if not check_dependencies():
        print("✗ فشل في التحقق من المتطلبات")
        return 1
    print("✓ جميع المتطلبات متوفرة")
    
    # إعداد البيئة
    print("إعداد البيئة...")
    setup_environment()
    print("✓ تم إعداد البيئة")
    
    # تشغيل التطبيق
    print("تشغيل التطبيق...")
    try:
        from ui.main_window import main as run_main_window
        return run_main_window()
    except Exception as e:
        print(f"✗ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
