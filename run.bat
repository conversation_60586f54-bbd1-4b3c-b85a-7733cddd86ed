@echo off
chcp 65001 >nul
title نظام تسجيل غياب الطلاب - مدرسة أبو عبيدة المتوسطة

echo ============================================================
echo 🏫 نظام تسجيل غياب الطلاب
echo 📚 Student Attendance Management System
echo ============================================================
echo 🎓 مدرسة أبو عبيدة المتوسطة
echo 🎓 Abu Ubaida Intermediate School
echo ============================================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo ❌ Error: Python not installed or not in PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo 📥 Please download and install Python from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM فحص وجود الملفات المطلوبة
if not exist "main_app.py" (
    echo ❌ خطأ: ملف main_app.py غير موجود
    echo ❌ Error: main_app.py file not found
    pause
    exit /b 1
)

if not exist "test_gui.py" (
    echo ❌ خطأ: ملف test_gui.py غير موجود
    echo ❌ Error: test_gui.py file not found
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo.

:menu
echo 📋 اختر طريقة التشغيل:
echo 1. التشغيل الرسمي (مع شاشة البداية)
echo 2. التشغيل المبسط (للاختبار السريع)
echo 3. تثبيت التبعيات
echo 4. خروج
echo.
set /p choice="👆 اختر رقم (1-4): "

if "%choice%"=="1" goto official
if "%choice%"=="2" goto simple
if "%choice%"=="3" goto install
if "%choice%"=="4" goto exit
echo ❌ خيار غير صحيح
echo.
goto menu

:official
echo.
echo 🚀 تشغيل التطبيق الرسمي...
echo 📂 تشغيل: python main_app.py
echo.
python main_app.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في تشغيل التطبيق
    echo 💡 جرب تثبيت التبعيات أولاً (الخيار 3)
)
echo.
pause
goto menu

:simple
echo.
echo 🚀 تشغيل التطبيق المبسط...
echo 📂 تشغيل: python test_gui.py
echo.
python test_gui.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في تشغيل التطبيق
    echo 💡 جرب تثبيت التبعيات أولاً (الخيار 3)
)
echo.
pause
goto menu

:install
echo.
echo 📦 تثبيت التبعيات المطلوبة...
echo.
echo 🔄 تثبيت PySide6...
python -m pip install PySide6
echo.
echo 🔄 تثبيت pandas...
python -m pip install pandas
echo.
echo 🔄 تثبيت openpyxl...
python -m pip install openpyxl
echo.
echo 🔄 تثبيت xlrd...
python -m pip install xlrd
echo.
echo 🔄 تثبيت python-docx...
python -m pip install python-docx
echo.
echo ✅ تم الانتهاء من تثبيت التبعيات
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام نظام تسجيل غياب الطلاب
echo 🎓 مدرسة أبو عبيدة المتوسطة
echo.
pause
exit /b 0
