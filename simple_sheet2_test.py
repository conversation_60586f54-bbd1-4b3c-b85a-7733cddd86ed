#!/usr/bin/env python3
"""
اختبار بسيط لقراءة Sheet2
"""

import pandas as pd

try:
    # قراءة جميع الشيتات من الملف
    excel_file = pd.ExcelFile('StudentGuidance.xls', engine='xlrd')
    print('الشيتات الموجودة:', excel_file.sheet_names)
    
    # قراءة Sheet2 تحديداً
    if 'Sheet2' in excel_file.sheet_names:
        df = pd.read_excel('StudentGuidance.xls', sheet_name='Sheet2', engine='xlrd')
        print('عدد الصفوف:', len(df))
        print('الأعمدة:', list(df.columns))
        print('أول 5 صفوف:')
        print(df.head())
        
    else:
        print('Sheet2 غير موجود')
        
except Exception as e:
    print('خطأ:', e)
