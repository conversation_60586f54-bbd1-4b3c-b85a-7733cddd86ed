#!/usr/bin/env python3
"""
فحص ملف Excel
Inspect Excel file
"""

import pandas as pd
from pathlib import Path

def inspect_excel_file():
    """فحص محتوى ملف Excel"""
    excel_file = Path("StudentGuidance.xls")
    
    if not excel_file.exists():
        print(f"ملف Excel غير موجود: {excel_file}")
        return
    
    print(f"فحص ملف: {excel_file}")
    
    try:
        # محاولة قراءة الملف بطرق مختلفة
        print("\n=== محاولة قراءة بـ openpyxl ===")
        try:
            df = pd.read_excel(excel_file, engine='openpyxl')
            print(f"نجح! الأبعاد: {df.shape}")
            print(f"الأعمدة: {list(df.columns)}")
            print("أول 5 صفوف:")
            print(df.head())
        except Exception as e:
            print(f"فشل: {e}")
        
        print("\n=== محاولة قراءة بـ xlrd ===")
        try:
            df = pd.read_excel(excel_file, engine='xlrd')
            print(f"نجح! الأبعاد: {df.shape}")
            print(f"الأعمدة: {list(df.columns)}")
            print("أول 5 صفوف:")
            print(df.head())
        except Exception as e:
            print(f"فشل: {e}")
        
        print("\n=== محاولة قراءة بدون header ===")
        try:
            df = pd.read_excel(excel_file, header=None, engine='xlrd')
            print(f"نجح! الأبعاد: {df.shape}")
            print(f"الأعمدة: {list(df.columns)}")
            print("أول 10 صفوف:")
            print(df.head(10))
        except Exception as e:
            print(f"فشل: {e}")
        
        print("\n=== محاولة قراءة كـ CSV ===")
        try:
            df = pd.read_csv(excel_file, encoding='utf-8')
            print(f"نجح! الأبعاد: {df.shape}")
            print(f"الأعمدة: {list(df.columns)}")
            print("أول 5 صفوف:")
            print(df.head())
        except Exception as e:
            print(f"فشل: {e}")
        
        print("\n=== محاولة قراءة كـ CSV مع encoding مختلف ===")
        try:
            df = pd.read_csv(excel_file, encoding='cp1256')
            print(f"نجح! الأبعاد: {df.shape}")
            print(f"الأعمدة: {list(df.columns)}")
            print("أول 5 صفوف:")
            print(df.head())
        except Exception as e:
            print(f"فشل: {e}")
            
    except Exception as e:
        print(f"خطأ عام: {e}")

if __name__ == "__main__":
    inspect_excel_file()
