#!/usr/bin/env python3
"""
اختبار استيراد البيانات من Sheet2 في ملف StudentGuidance.xls
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd() / 'src'))

def test_sheet2_import():
    """اختبار استيراد البيانات من Sheet2"""
    try:
        from services.excel_import_service import ExcelImportService
        from data.database_manager import DatabaseManager
        
        print("🔍 بدء اختبار استيراد Sheet2...")
        
        # إنشاء خدمة الاستيراد
        db_manager = DatabaseManager()
        import_service = ExcelImportService(db_manager)
        
        # اختبار قراءة ملف StudentGuidance.xls (سيستخدم Sheet2 تلقائياً)
        file_path = 'StudentGuidance.xls'
        print(f"📂 محاولة استيراد البيانات من {file_path}...")
        
        # استيراد البيانات (سيستخدم Sheet2 تلقائياً للملف StudentGuidance)
        result = import_service.import_students_from_excel(file_path)
        
        if result['success']:
            data = result['data']
            print(f"✅ تم استيراد البيانات بنجاح!")
            print(f"   📊 عدد الطلاب المستوردين: {data['successful_count']}")
            print(f"   ❌ عدد الطلاب الفاشلين: {data['failed_count']}")
            print(f"   📈 إجمالي المعالجة: {data['total_processed']}")
            
            if data['successful_count'] > 0:
                print(f"\n🎉 تم استيراد {data['successful_count']} طالب من StudentGuidance.xls بنجاح!")
                
                # عرض بعض الطلاب المستوردين
                if data.get('imported_students'):
                    print("\n👥 أمثلة على الطلاب المستوردين:")
                    for i, student in enumerate(data['imported_students'][:5]):
                        print(f"   {i+1}. {student.get('name', 'غير محدد')} - {student.get('grade_code', 'غير محدد')}")
                        
            else:
                print("\n❌ لم يتم استيراد أي طالب من StudentGuidance.xls")
                if data.get('failed_imports'):
                    print("🔍 أسباب الفشل:")
                    for i, failed in enumerate(data['failed_imports'][:3]):
                        print(f"   {i+1}. {failed.get('reason', 'سبب غير محدد')}")
        else:
            print(f"❌ فشل في استيراد البيانات: {result.get('message', 'خطأ غير محدد')}")
            
    except Exception as e:
        print(f"💥 خطأ في اختبار الاستيراد: {e}")
        import traceback
        traceback.print_exc()

def test_manual_sheet2():
    """اختبار قراءة Sheet2 يدوياً"""
    try:
        import pandas as pd
        
        print("\n🔍 اختبار قراءة Sheet2 يدوياً...")
        
        # قراءة جميع الشيتات
        excel_file = pd.ExcelFile('StudentGuidance.xls', engine='xlrd')
        print(f"📋 الشيتات الموجودة: {excel_file.sheet_names}")
        
        # قراءة Sheet2 تحديداً
        if 'Sheet2' in excel_file.sheet_names:
            df = pd.read_excel('StudentGuidance.xls', sheet_name='Sheet2', engine='xlrd')
            print(f"📊 بيانات Sheet2:")
            print(f"   📈 عدد الصفوف: {len(df)}")
            print(f"   📋 الأعمدة: {list(df.columns)}")
            
            # عرض أول 5 صفوف
            print(f"\n📄 أول 5 صفوف:")
            print(df.head())
            
            # فحص البيانات غير الفارغة
            print(f"\n📊 إحصائيات البيانات:")
            print(df.info())
            
        else:
            print("❌ Sheet2 غير موجود في الملف")
            
    except Exception as e:
        print(f"💥 خطأ في قراءة Sheet2: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار استيراد البيانات من Sheet2")
    print("=" * 50)
    
    # اختبار قراءة Sheet2 يدوياً أولاً
    test_manual_sheet2()
    
    print("\n" + "=" * 50)
    
    # اختبار الاستيراد عبر الخدمة
    test_sheet2_import()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
