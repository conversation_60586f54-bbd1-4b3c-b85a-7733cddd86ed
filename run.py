#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل المبسط - نظام تسجيل غياب الطلاب
Simple Launcher - Student Attendance System

مدرسة أبو عبيدة المتوسطة
Abu Ubaida Intermediate School
"""

import sys
import os
import subprocess
from pathlib import Path

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🏫 نظام تسجيل غياب الطلاب")
    print("📚 Student Attendance Management System")
    print("=" * 60)
    print("🎓 مدرسة أبو عبيدة المتوسطة")
    print("🎓 Abu Ubaida Intermediate School")
    print("=" * 60)

def check_python():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("❌ Error: Python 3.8+ required")
        print(f"📍 الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - متوافق")
    return True

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print("\n🔍 فحص التبعيات المطلوبة...")
    
    required_packages = {
        'PySide6': 'PySide6',
        'pandas': 'pandas', 
        'openpyxl': 'openpyxl',
        'xlrd': 'xlrd',
        'docx': 'python-docx'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n⚠️  التبعيات المفقودة: {len(missing_packages)}")
        print("📦 لتثبيت التبعيات المفقودة، استخدم:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ جميع التبعيات متوفرة")
    return True

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n📋 اختر طريقة التشغيل:")
    print("1️⃣  التشغيل الرسمي (مع شاشة البداية)")
    print("2️⃣  التشغيل المبسط (للاختبار السريع)")
    print("3️⃣  تثبيت التبعيات")
    print("4️⃣  عرض معلومات النظام")
    print("5️⃣  خروج")
    print("-" * 40)

def install_dependencies():
    """تثبيت التبعيات"""
    print("\n📦 تثبيت التبعيات...")
    packages = ['PySide6', 'pandas', 'openpyxl', 'xlrd', 'python-docx']
    
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + packages
        print(f"🔄 تنفيذ: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع التبعيات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت التبعيات")
            print(f"خطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("\n💻 معلومات النظام:")
    print(f"🐍 Python: {sys.version}")
    print(f"💾 النظام: {os.name}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    # فحص الملفات المطلوبة
    required_files = ['main_app.py', 'test_gui.py', 'src/', 'data/']
    print(f"\n📂 الملفات المطلوبة:")
    
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")

def run_application(mode='official'):
    """تشغيل التطبيق"""
    if mode == 'official':
        script = 'main_app.py'
        print("\n🚀 تشغيل التطبيق الرسمي...")
    else:
        script = 'test_gui.py'
        print("\n🚀 تشغيل التطبيق المبسط...")
    
    if not Path(script).exists():
        print(f"❌ خطأ: الملف {script} غير موجود")
        return False
    
    try:
        print(f"📂 تشغيل: python3 {script}")
        subprocess.run([sys.executable, script])
        return True
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python():
        input("\n⏸️  اضغط Enter للخروج...")
        return 1
    
    while True:
        show_menu()
        
        try:
            choice = input("👆 اختر رقم (1-5): ").strip()
            
            if choice == '1':
                # التشغيل الرسمي
                if check_dependencies():
                    run_application('official')
                else:
                    print("\n⚠️  يجب تثبيت التبعيات أولاً")
                    
            elif choice == '2':
                # التشغيل المبسط
                if check_dependencies():
                    run_application('simple')
                else:
                    print("\n⚠️  يجب تثبيت التبعيات أولاً")
                    
            elif choice == '3':
                # تثبيت التبعيات
                install_dependencies()
                
            elif choice == '4':
                # معلومات النظام
                show_system_info()
                
            elif choice == '5':
                # خروج
                print("\n👋 شكراً لاستخدام نظام تسجيل غياب الطلاب")
                print("🎓 مدرسة أبو عبيدة المتوسطة")
                break
                
            else:
                print("❌ خيار غير صحيح، يرجى اختيار رقم من 1 إلى 5")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
        
        input("\n⏸️  اضغط Enter للمتابعة...")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 خطأ فادح: {e}")
        input("⏸️  اضغط Enter للخروج...")
        sys.exit(1)
