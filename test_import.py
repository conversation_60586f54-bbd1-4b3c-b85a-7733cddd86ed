#!/usr/bin/env python3
"""
اختبار بسيط لاستيراد البيانات
Simple test for data import
"""

import sys
import os
from pathlib import Path

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """اختبار الاستيراد"""
    try:
        print("اختبار استيراد الوحدات...")
        
        # اختبار استيراد المكونات الأساسية
        from utils.config import Config
        print("✓ تم استيراد Config")
        
        from utils.logger import get_logger
        print("✓ تم استيراد get_logger")
        
        from data.database_manager import DatabaseManager
        print("✓ تم استيراد DatabaseManager")
        
        from services.excel_import_service import ExcelImportService
        print("✓ تم استيراد ExcelImportService")
        
        # اختبار تهيئة قاعدة البيانات
        config = Config()
        db_manager = DatabaseManager(config)
        
        print("✓ تم إنشاء DatabaseManager")
        
        # اختبار الاتصال
        db_manager.initialize_database()
        print("✓ تم تهيئة قاعدة البيانات")
        
        # اختبار خدمة الاستيراد
        import_service = ExcelImportService(db_manager)
        print("✓ تم إنشاء ExcelImportService")
        
        # البحث عن ملف البيانات
        data_file = project_root / "StudentGuidance.xls"
        if data_file.exists():
            print(f"✓ تم العثور على ملف البيانات: {data_file}")
            
            # معاينة البيانات
            try:
                preview = import_service.preview_excel_import(str(data_file))
                if preview["success"]:
                    file_info = preview["data"]["file_info"]
                    print(f"✓ معاينة البيانات: {file_info['total_rows']} صف")
                    print(f"  الأعمدة: {preview['data']['available_columns']}")
                else:
                    print("✗ فشل في معاينة البيانات")
            except Exception as e:
                print(f"✗ خطأ في معاينة البيانات: {e}")
        else:
            print(f"✗ ملف البيانات غير موجود: {data_file}")
        
        db_manager.close()
        print("✓ تم إغلاق قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=== اختبار استيراد البيانات ===")
    
    if test_imports():
        print("\n✓ جميع الاختبارات نجحت!")
        return 0
    else:
        print("\n✗ فشل في بعض الاختبارات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
