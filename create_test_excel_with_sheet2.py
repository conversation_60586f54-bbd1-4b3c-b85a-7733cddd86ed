#!/usr/bin/env python3
"""
إنشاء ملف Excel اختبار مع Sheet2 لاختبار الوظيفة
"""

import pandas as pd
import sys
from pathlib import Path

def create_test_excel():
    """إنشاء ملف Excel اختبار مع Sheet2"""
    try:
        # بيانات اختبار للشيت الأول
        sheet1_data = {
            'معلومات': ['هذا الشيت الأول', 'لا يحتوي على بيانات الطلاب'],
            'ملاحظات': ['Sheet1', 'تجاهل هذا الشيت']
        }
        
        # بيانات اختبار للشيت الثاني (بيانات الطلاب)
        sheet2_data = {
            'اسم الطالب': [
                'أحمد محمد علي',
                'فاطمة أحمد سالم', 
                'محمد عبدالله حسن',
                'عائشة سعد محمد',
                'علي حسن أحمد'
            ],
            'رقم الهوية': [
                '1234567890',
                '2345678901', 
                '3456789012',
                '4567890123',
                '5678901234'
            ],
            'رمز الصف': [
                '1A',
                '1B',
                '2A', 
                '2B',
                '3A'
            ],
            'رقم الطالب': [
                '001',
                '002',
                '003',
                '004', 
                '005'
            ]
        }
        
        # إنشاء ملف Excel مع شيتين
        with pd.ExcelWriter('TestStudentGuidance.xlsx', engine='openpyxl') as writer:
            # كتابة الشيت الأول
            df1 = pd.DataFrame(sheet1_data)
            df1.to_excel(writer, sheet_name='Sheet1', index=False)
            
            # كتابة الشيت الثاني (بيانات الطلاب)
            df2 = pd.DataFrame(sheet2_data)
            df2.to_excel(writer, sheet_name='Sheet2', index=False)
        
        print("✅ تم إنشاء ملف TestStudentGuidance.xlsx بنجاح")
        print("📋 الشيت الأول: معلومات عامة")
        print("📊 الشيت الثاني: بيانات الطلاب")
        
        # التحقق من الملف
        excel_file = pd.ExcelFile('TestStudentGuidance.xlsx')
        print(f"\n🔍 الشيتات الموجودة: {excel_file.sheet_names}")
        
        # قراءة Sheet2
        df_sheet2 = pd.read_excel('TestStudentGuidance.xlsx', sheet_name='Sheet2')
        print(f"\n📊 بيانات Sheet2:")
        print(f"عدد الصفوف: {len(df_sheet2)}")
        print(f"الأعمدة: {list(df_sheet2.columns)}")
        print("\nأول 3 صفوف:")
        print(df_sheet2.head(3))
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_service():
    """اختبار خدمة الاستيراد مع الملف الجديد"""
    try:
        sys.path.insert(0, str(Path.cwd() / 'src'))
        
        from services.excel_import_service import ExcelImportService
        from data.database_manager import DatabaseManager
        
        print("\n🧪 اختبار خدمة الاستيراد...")
        
        # إنشاء خدمة الاستيراد
        db_manager = DatabaseManager()
        import_service = ExcelImportService(db_manager)
        
        # اختبار استيراد الملف الجديد
        file_path = 'TestStudentGuidance.xlsx'
        print(f"📂 استيراد من {file_path}...")
        
        # استيراد البيانات (سيستخدم Sheet2 تلقائياً لأن اسم الملف يحتوي على StudentGuidance)
        result = import_service.import_students_from_excel(file_path)
        
        if result['success']:
            data = result['data']
            print(f"✅ تم الاستيراد بنجاح!")
            print(f"   📊 عدد الطلاب المستوردين: {data['successful_count']}")
            print(f"   ❌ عدد الطلاب الفاشلين: {data['failed_count']}")
            print(f"   📈 إجمالي المعالجة: {data['total_processed']}")
        else:
            print(f"❌ فشل الاستيراد: {result.get('message', 'خطأ غير محدد')}")
            
    except Exception as e:
        print(f"💥 خطأ في اختبار الخدمة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 إنشاء واختبار ملف Excel مع Sheet2")
    print("=" * 50)
    
    # إنشاء الملف
    if create_test_excel():
        # اختبار الخدمة
        test_import_service()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
