#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys

def check_excel_file():
    try:
        print("🔍 فحص ملف StudentGuidance.xls...")
        
        # قراءة جميع الشيتات
        excel_file = pd.ExcelFile('StudentGuidance.xls', engine='xlrd')
        sheets = excel_file.sheet_names
        print(f"📋 الشيتات الموجودة: {sheets}")
        
        # فحص Sheet2
        if 'Sheet2' in sheets:
            print("\n✅ تم العثور على Sheet2")
            df = pd.read_excel('StudentGuidance.xls', sheet_name='Sheet2', engine='xlrd')
            
            print(f"📊 عدد الصفوف: {len(df)}")
            print(f"📊 عدد الأعمدة: {len(df.columns)}")
            print(f"📊 أسماء الأعمدة: {list(df.columns)}")
            
            # عرض أول 5 صفوف
            print("\n📋 أول 5 صفوف:")
            print(df.head())
            
            # فحص البيانات الفارغة
            print("\n📊 إحصائيات البيانات:")
            print(df.info())
            
            # فحص القيم الفريدة في الأعمدة المهمة
            for col in df.columns:
                if 'صف' in col.lower() or 'grade' in col.lower():
                    print(f"\n🔍 القيم الفريدة في عمود '{col}':")
                    print(df[col].value_counts().head(10))
                    
        else:
            print("❌ Sheet2 غير موجود")
            print("الشيتات المتاحة:")
            for i, sheet in enumerate(sheets):
                print(f"  {i+1}. {sheet}")
                
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_excel_file()
