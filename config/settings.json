{"application": {"name": "برنامج تسجيل غياب الطلاب", "version": "1.0.0", "author": "متوسطة أبي عبيدة", "language": "ar", "direction": "rtl"}, "database": {"path": "data/database.db", "backup_path": "data/backups/", "auto_backup": true, "backup_interval_hours": 24, "max_backups": 30}, "school": {"name": "متوسطة أبي عبيدة", "stage": "متوسط", "stage_code": "2", "academic_year": "1446-1447", "current_semester": "الأول"}, "ui": {"theme": "default", "font_family": "<PERSON><PERSON>", "font_size": 12, "window_width": 1200, "window_height": 800, "language": "ar", "direction": "rtl"}, "reports": {"default_template": "official", "date_format": "hijri", "export_format": "docx", "export_path": "data/exports/", "show_logo": true, "logo_path": "src/ui/resources/icons/logo.png"}, "grades": {"0725": {"name": "الأول متوسط", "level": 1, "stage": "متوسط"}, "0825": {"name": "الثاني متوسط", "level": 2, "stage": "متوسط"}, "0925": {"name": "الثالث متوسط", "level": 3, "stage": "متوسط"}}, "logging": {"level": "INFO", "file_path": "logs/app.log", "max_file_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"enable_user_auth": false, "session_timeout": 3600, "password_min_length": 8}}