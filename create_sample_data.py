#!/usr/bin/env python3
"""
إنشاء بيانات تجريبية للطلاب
Create sample student data
"""

import pandas as pd
from pathlib import Path

def create_sample_student_data():
    """إنشاء بيانات تجريبية للطلاب"""
    
    # بيانات الطلاب التجريبية
    students_data = [
        {
            "اسم الطالب": "أحمد محمد علي السعد",
            "رقم الهوية": "1234567890",
            "الصف": "1A",
            "رقم الجوال": "0501234567",
            "جوال ولي الأمر": "0509876543",
            "العنوان": "الرياض - حي النخيل"
        },
        {
            "اسم الطالب": "محمد عبدالله أحمد",
            "رقم الهوية": "2345678901",
            "الصف": "1A",
            "رقم الجوال": "0502345678",
            "جوال ولي الأمر": "0508765432",
            "العنوان": "الرياض - حي الملز"
        },
        {
            "اسم الطالب": "عبدالرحمن سعد محمد",
            "رقم الهوية": "3456789012",
            "الصف": "1B",
            "رقم الجوال": "0503456789",
            "جوال ولي الأمر": "0507654321",
            "العنوان": "الرياض - حي العليا"
        },
        {
            "اسم الطالب": "فيصل خالد عبدالعزيز",
            "رقم الهوية": "4567890123",
            "الصف": "1B",
            "رقم الجوال": "0504567890",
            "جوال ولي الأمر": "0506543210",
            "العنوان": "الرياض - حي الورود"
        },
        {
            "اسم الطالب": "عبدالله فهد سليمان",
            "رقم الهوية": "5678901234",
            "الصف": "2A",
            "رقم الجوال": "0505678901",
            "جوال ولي الأمر": "0505432109",
            "العنوان": "الرياض - حي الصحافة"
        },
        {
            "اسم الطالب": "سعد عبدالرحمن محمد",
            "رقم الهوية": "6789012345",
            "الصف": "2A",
            "رقم الجوال": "0506789012",
            "جوال ولي الأمر": "0504321098",
            "العنوان": "الرياض - حي الياسمين"
        },
        {
            "اسم الطالب": "خالد أحمد عبدالله",
            "رقم الهوية": "7890123456",
            "الصف": "2B",
            "رقم الجوال": "0507890123",
            "جوال ولي الأمر": "0503210987",
            "العنوان": "الرياض - حي الربيع"
        },
        {
            "اسم الطالب": "عبدالعزيز محمد فهد",
            "رقم الهوية": "8901234567",
            "الصف": "2B",
            "رقم الجوال": "0508901234",
            "جوال ولي الأمر": "0502109876",
            "العنوان": "الرياض - حي النرجس"
        },
        {
            "اسم الطالب": "فهد سعد خالد",
            "رقم الهوية": "9012345678",
            "الصف": "3A",
            "رقم الجوال": "0509012345",
            "جوال ولي الأمر": "0501098765",
            "العنوان": "الرياض - حي الفلاح"
        },
        {
            "اسم الطالب": "سليمان عبدالله أحمد",
            "رقم الهوية": "0123456789",
            "الصف": "3A",
            "رقم الجوال": "0500123456",
            "جوال ولي الأمر": "0500987654",
            "العنوان": "الرياض - حي الشفا"
        },
        {
            "اسم الطالب": "محمد فيصل عبدالعزيز",
            "رقم الهوية": "1122334455",
            "الصف": "3B",
            "رقم الجوال": "0501122334",
            "جوال ولي الأمر": "0509988776",
            "العنوان": "الرياض - حي الروضة"
        },
        {
            "اسم الطالب": "أحمد عبدالرحمن سعد",
            "رقم الهوية": "2233445566",
            "الصف": "3B",
            "رقم الجوال": "0502233445",
            "جوال ولي الأمر": "0508877665",
            "العنوان": "الرياض - حي الحمراء"
        },
        {
            "اسم الطالب": "عبدالله محمد خالد",
            "رقم الهوية": "3344556677",
            "الصف": "1A",
            "رقم الجوال": "0503344556",
            "جوال ولي الأمر": "0507766554",
            "العنوان": "الرياض - حي السليمانية"
        },
        {
            "اسم الطالب": "خالد فهد عبدالله",
            "رقم الهوية": "4455667788",
            "الصف": "1B",
            "رقم الجوال": "0504455667",
            "جوال ولي الأمر": "0506655443",
            "العنوان": "الرياض - حي الملقا"
        },
        {
            "اسم الطالب": "سعد محمد فيصل",
            "رقم الهوية": "5566778899",
            "الصف": "2A",
            "رقم الجوال": "0505566778",
            "جوال ولي الأمر": "0505544332",
            "العنوان": "الرياض - حي العارض"
        },
        {
            "اسم الطالب": "فيصل عبدالعزيز أحمد",
            "رقم الهوية": "6677889900",
            "الصف": "2B",
            "رقم الجوال": "0506677889",
            "جوال ولي الأمر": "0504433221",
            "العنوان": "الرياض - حي الغدير"
        },
        {
            "اسم الطالب": "عبدالرحمن خالد محمد",
            "رقم الهوية": "7788990011",
            "الصف": "3A",
            "رقم الجوال": "0507788990",
            "جوال ولي الأمر": "0503322110",
            "العنوان": "الرياض - حي الإزدهار"
        },
        {
            "اسم الطالب": "محمد سليمان عبدالله",
            "رقم الهوية": "8899001122",
            "الصف": "3B",
            "رقم الجوال": "0508899001",
            "جوال ولي الأمر": "0502211009",
            "العنوان": "الرياض - حي الواحة"
        },
        {
            "اسم الطالب": "عبدالعزيز أحمد سعد",
            "رقم الهوية": "9900112233",
            "الصف": "1A",
            "رقم الجوال": "0509900112",
            "جوال ولي الأمر": "0501100998",
            "العنوان": "الرياض - حي الندى"
        },
        {
            "اسم الطالب": "أحمد عبدالله فهد",
            "رقم الهوية": "0011223344",
            "الصف": "1B",
            "رقم الجوال": "0500011223",
            "جوال ولي الأمر": "0500099887",
            "العنوان": "الرياض - حي الرحمانية"
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(students_data)
    
    # حفظ الملف
    output_file = Path("StudentData.xlsx")
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"✓ تم إنشاء ملف البيانات التجريبية: {output_file}")
    print(f"عدد الطلاب: {len(students_data)}")
    print(f"الأعمدة: {list(df.columns)}")
    
    # عرض إحصائيات
    grade_counts = df['الصف'].value_counts()
    print("\nتوزيع الطلاب حسب الصف:")
    for grade, count in grade_counts.items():
        print(f"  {grade}: {count} طالب")
    
    return str(output_file)

def main():
    """الدالة الرئيسية"""
    print("=== إنشاء بيانات تجريبية للطلاب ===")
    
    file_path = create_sample_student_data()
    
    print(f"\nتم إنشاء الملف بنجاح: {file_path}")
    print("يمكنك الآن استخدام هذا الملف لاختبار نظام الاستيراد")

if __name__ == "__main__":
    main()
