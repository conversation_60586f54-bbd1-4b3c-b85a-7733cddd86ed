# 📚 برنامج تسجيل غياب الطلاب - نظام احترافي متكامل

## 🎯 نظرة عامة

برنامج احترافي متكامل لإدارة وتسجيل غياب الطلاب في المدارس المتوسطة، مصمم خصيصاً لمتوسطة أبي عبيدة. يوفر البرنامج واجهة سهلة الاستخدام مع دعم كامل للغة العربية والتاريخ الهجري.

## ✨ الميزات الرئيسية

### 📝 تسجيل الغياب
- **اختيار سريع للطلاب** من قائمة منظمة حسب الصف
- **تعبئة تلقائية للتاريخ الهجري** مع إمكانية التعديل
- **تسجيل الغياب بعذر أو بدون عذر** مع إدخال السبب
- **حفظ فوري** مع رسائل تأكيد

### 📊 نظام التقارير المتقدم
- **تقارير متنوعة**: طالب محدد، صف، يومي، شهري، فصلي
- **معاينة فورية** للتقارير قبل التصدير
- **تصدير احترافي إلى Word** مع تنسيق متقدم
- **إحصائيات شاملة** ونسب الغياب

### 🎨 مصمم التقارير
- **تصميم مخصص للهيدر والفوتر**
- **اختيار الخطوط والألوان**
- **إضافة الشعار والعناصر الرسمية**
- **حفظ قوالب متعددة**

### ⚙️ إدارة البيانات
- **استيراد بيانات الطلاب من Excel**
- **نسخ احتياطية تلقائية ويدوية**
- **استعادة البيانات**
- **تحديث معلومات المدرسة**

## 📊 البيانات الحالية

### معلومات المدرسة
- **اسم المدرسة**: متوسطة أبي عبيدة
- **المرحلة**: متوسط

### إحصائيات الطلاب
- **إجمالي الطلاب**: 147 طالب
- **الصف الأول متوسط (0725)**: 38 طالب
- **الصف الثاني متوسط (0825)**: 55 طالب
- **الصف الثالث متوسط (0925)**: 54 طالب

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **PySide6 (Qt)**: واجهة المستخدم الرسومية
- **SQLite**: قاعدة البيانات المحلية
- **pandas**: معالجة ملفات Excel
- **python-docx**: إنشاء ملفات Word
- **hijri-converter**: تحويل التواريخ الهجرية

## 📦 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python >= 3.8
Qt >= 6.5
SQLite >= 3.35
```

### 2. تحميل المشروع
```bash
git clone [repository-url]
cd student_attendance_system
```

### 3. إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/macOS
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. تشغيل البرنامج
```bash
python src/main.py
```

## 🏗️ هيكل المشروع

```
student_attendance_system/
├── src/
│   ├── models/          # نماذج البيانات
│   ├── data/            # طبقة الوصول للبيانات
│   ├── services/        # منطق الأعمال
│   ├── ui/              # واجهة المستخدم
│   ├── utils/           # الأدوات المساعدة
│   └── main.py          # نقطة الدخول
├── data/                # ملفات البيانات
├── tests/               # الاختبارات
├── docs/                # الوثائق
└── config/              # الإعدادات
```

## 🚀 كيفية الاستخدام

### 1. تسجيل غياب طالب
1. افتح تبويب "تسجيل الغياب"
2. اختر الطالب من القائمة
3. تأكد من التاريخ الهجري
4. حدد نوع الغياب (بعذر/بدون عذر)
5. أدخل السبب إذا كان بعذر
6. اضغط "حفظ"

### 2. إنشاء تقرير
1. افتح تبويب "التقارير"
2. اختر نوع التقرير
3. حدد الفترة الزمنية
4. اضغط "إنشاء التقرير"
5. معاينة النتائج
6. تصدير إلى Word

### 3. استيراد بيانات الطلاب
1. افتح تبويب "الإعدادات"
2. اختر "استيراد من Excel"
3. حدد ملف Excel
4. تأكد من صحة البيانات
5. اضغط "استيراد"

## 🔧 الإعدادات المتقدمة

### إعدادات قاعدة البيانات
```json
{
  "database": {
    "path": "data/database.db",
    "backup_interval": 24,
    "auto_backup": true
  }
}
```

### إعدادات التقارير
```json
{
  "reports": {
    "default_template": "official",
    "date_format": "hijri",
    "export_format": "docx"
  }
}
```

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_models/

# تشغيل مع تقرير التغطية
pytest --cov=src
```

## 📝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## 📄 الرخصة

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم والمساعدة

### المشاكل الشائعة

**مشكلة**: لا يمكن قراءة ملف Excel
**الحل**: تأكد من أن الملف بصيغة .xls أو .xlsx وغير محمي بكلمة مرور

**مشكلة**: خطأ في التاريخ الهجري
**الحل**: تأكد من تثبيت مكتبة hijri-converter بشكل صحيح

**مشكلة**: لا يمكن تصدير التقرير
**الحل**: تأكد من وجود صلاحيات الكتابة في مجلد التصدير

### التواصل
- **البريد الإلكتروني**: [email]
- **الهاتف**: [phone]
- **الموقع**: [website]

## 🔄 التحديثات المستقبلية

- [ ] دعم قاعدة بيانات MySQL
- [ ] تطبيق ويب
- [ ] تطبيق موبايل
- [ ] تقارير PDF
- [ ] إشعارات تلقائية
- [ ] نظام المستخدمين المتعدد

---

**تم تطوير هذا البرنامج خصيصاً لمتوسطة أبي عبيدة بمعايير احترافية عالية** 🏫✨
