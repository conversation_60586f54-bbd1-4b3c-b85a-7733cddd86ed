#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل السريع - نظام تسجيل غياب الطلاب
Quick Start Launcher - Student Attendance System

مدرسة أبو عبيدة المتوسطة
Abu Ubaida Intermediate School
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """تشغيل سريع للتطبيق"""
    
    print("🏫 نظام تسجيل غياب الطلاب")
    print("🎓 مدرسة أبو عبيدة المتوسطة")
    print("=" * 50)
    
    # فحص الملفات
    if Path('main_app.py').exists():
        print("🚀 تشغيل التطبيق الرسمي...")
        try:
            subprocess.run([sys.executable, 'main_app.py'])
        except KeyboardInterrupt:
            print("\n⚠️  تم إيقاف التطبيق")
        except Exception as e:
            print(f"❌ خطأ: {e}")
            print("\n🔄 محاولة التشغيل المبسط...")
            if Path('test_gui.py').exists():
                subprocess.run([sys.executable, 'test_gui.py'])
    
    elif Path('test_gui.py').exists():
        print("🚀 تشغيل التطبيق المبسط...")
        try:
            subprocess.run([sys.executable, 'test_gui.py'])
        except KeyboardInterrupt:
            print("\n⚠️  تم إيقاف التطبيق")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    else:
        print("❌ لم يتم العثور على ملفات التطبيق")
        print("تأكد من وجود main_app.py أو test_gui.py")

if __name__ == "__main__":
    main()
