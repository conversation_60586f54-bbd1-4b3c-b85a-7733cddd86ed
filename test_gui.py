#!/usr/bin/env python3
"""
اختبار سريع للواجهة الرئيسية
Quick test for main GUI
"""

import sys
import os
from pathlib import Path
from datetime import date

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QComboBox,
    QDateEdit, QGroupBox, QStatusBar, QMessageBox, QCheckBox, QLineEdit
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

class SimpleMainWindow(QMainWindow):
    """نافذة رئيسية مبسطة للاختبار"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_sample_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام تسجيل غياب الطلاب - مدرسة أبو عبيدة المتوسطة")
        self.setGeometry(100, 100, 1000, 700)
        
        # إعداد الخط العربي
        arabic_font = QFont("Arial", 12)
        arabic_font.setFamily("Tahoma")
        self.setFont(arabic_font)
        
        # إعداد اتجاه النص (من اليمين لليسار)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # قسم التاريخ
        date_group = QGroupBox("معلومات التاريخ")
        date_layout = QHBoxLayout(date_group)
        
        date_layout.addWidget(QLabel("التاريخ الميلادي:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        
        date_layout.addWidget(QLabel("التاريخ الهجري:"))
        self.hijri_label = QLabel("1446/12/24")
        date_layout.addWidget(self.hijri_label)
        
        date_layout.addStretch()
        main_layout.addWidget(date_group)
        
        # قسم تسجيل الغياب
        attendance_group = QGroupBox("تسجيل الغياب")
        attendance_layout = QVBoxLayout(attendance_group)
        
        # أدوات التحكم
        controls_layout = QHBoxLayout()
        
        controls_layout.addWidget(QLabel("الصف:"))
        self.grade_filter = QComboBox()
        self.grade_filter.addItems(["جميع الصفوف", "1أ", "1ب", "2أ", "2ب", "3أ", "3ب"])
        controls_layout.addWidget(self.grade_filter)
        
        controls_layout.addWidget(QLabel("البحث:"))
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("ابحث عن طالب...")
        controls_layout.addWidget(self.search_box)
        
        controls_layout.addStretch()
        
        mark_all_btn = QPushButton("تحديد الكل")
        mark_all_btn.clicked.connect(self.mark_all_absent)
        controls_layout.addWidget(mark_all_btn)
        
        clear_all_btn = QPushButton("إلغاء الكل")
        clear_all_btn.clicked.connect(self.clear_all_absent)
        controls_layout.addWidget(clear_all_btn)
        
        attendance_layout.addLayout(controls_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(6)
        self.students_table.setHorizontalHeaderLabels([
            "غائب", "اسم الطالب", "رقم الهوية", "الصف", "نوع الغياب", "السبب"
        ])
        
        attendance_layout.addWidget(self.students_table)
        main_layout.addWidget(attendance_group)
        
        # قسم الإحصائيات
        stats_group = QGroupBox("إحصائيات سريعة")
        stats_layout = QHBoxLayout(stats_group)
        
        self.total_students_label = QLabel("إجمالي الطلاب: 0")
        stats_layout.addWidget(self.total_students_label)
        
        self.present_students_label = QLabel("الحاضرون: 0")
        stats_layout.addWidget(self.present_students_label)
        
        self.absent_students_label = QLabel("الغائبون: 0")
        stats_layout.addWidget(self.absent_students_label)
        
        self.absence_rate_label = QLabel("نسبة الغياب: 0%")
        stats_layout.addWidget(self.absence_rate_label)
        
        stats_layout.addStretch()
        
        save_btn = QPushButton("حفظ الغياب")
        save_btn.clicked.connect(self.save_absences)
        stats_layout.addWidget(save_btn)
        
        main_layout.addWidget(stats_group)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز")
    
    def load_sample_data(self):
        """تحميل بيانات تجريبية"""
        sample_students = [
            ("أحمد محمد العلي", "1234567890", "1أ"),
            ("فاطمة عبدالله السالم", "2345678901", "1أ"),
            ("محمد أحمد الخالد", "3456789012", "1ب"),
            ("عائشة سعد المطيري", "4567890123", "1ب"),
            ("عبدالرحمن علي القحطاني", "5678901234", "2أ"),
            ("خديجة محمد الشهري", "6789012345", "2أ"),
            ("يوسف عبدالعزيز النجار", "7890123456", "2ب"),
            ("مريم سالم الدوسري", "8901234567", "2ب"),
            ("عبدالله فهد الغامدي", "9012345678", "3أ"),
            ("زينب حسن العتيبي", "0123456789", "3أ"),
        ]
        
        self.students_table.setRowCount(len(sample_students))
        
        for row, (name, civil_id, grade) in enumerate(sample_students):
            # عمود الغياب
            absent_checkbox = QCheckBox()
            absent_checkbox.stateChanged.connect(self.update_statistics)
            self.students_table.setCellWidget(row, 0, absent_checkbox)
            
            # اسم الطالب
            self.students_table.setItem(row, 1, QTableWidgetItem(name))
            
            # رقم الهوية
            self.students_table.setItem(row, 2, QTableWidgetItem(civil_id))
            
            # الصف
            self.students_table.setItem(row, 3, QTableWidgetItem(grade))
            
            # نوع الغياب
            absence_type_combo = QComboBox()
            absence_type_combo.addItems(["غياب", "غياب بعذر", "تأخير", "انصراف مبكر"])
            self.students_table.setCellWidget(row, 4, absence_type_combo)
            
            # السبب
            reason_edit = QLineEdit()
            reason_edit.setPlaceholderText("اختياري...")
            self.students_table.setCellWidget(row, 5, reason_edit)
        
        self.update_statistics()
    
    def mark_all_absent(self):
        """تحديد جميع الطلاب كغائبين"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.update_statistics()
    
    def clear_all_absent(self):
        """إلغاء تحديد جميع الطلاب"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.update_statistics()
    
    def save_absences(self):
        """حفظ بيانات الغياب"""
        absent_count = 0
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                absent_count += 1
        
        QMessageBox.information(
            self, 
            "تم الحفظ", 
            f"تم حفظ {absent_count} غياب بنجاح"
        )
        self.status_bar.showMessage(f"تم حفظ {absent_count} غياب", 3000)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_students = self.students_table.rowCount()
        absent_count = 0
        
        for row in range(total_students):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                absent_count += 1
        
        present_count = total_students - absent_count
        
        self.total_students_label.setText(f"إجمالي الطلاب: {total_students}")
        self.present_students_label.setText(f"الحاضرون: {present_count}")
        self.absent_students_label.setText(f"الغائبون: {absent_count}")
        
        if total_students > 0:
            absence_rate = (absent_count / total_students) * 100
            self.absence_rate_label.setText(f"نسبة الغياب: {absence_rate:.1f}%")
        else:
            self.absence_rate_label.setText("نسبة الغياب: 0%")

def main():
    """الدالة الرئيسية"""
    print("=== نظام تسجيل غياب الطلاب ===")
    print("مدرسة أبو عبيدة المتوسطة")
    print("=" * 40)
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = SimpleMainWindow()
    window.show()
    
    print("✓ تم تشغيل التطبيق بنجاح")
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
