#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تسجيل غياب الطلاب - التطبيق الرسمي
Student Attendance System - Official Application

مدرسة أبو عبيدة المتوسطة
Abu Ubaida Intermediate School

تطوير: نظام ذكي لإدارة غياب الطلاب
Development: Smart Student Attendance Management System
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from PySide6.QtWidgets import QApplication, QSplashScreen, QMessageBox
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    required_modules = [
        'PySide6',
        'sqlite3',
        'pandas',
        'openpyxl',
        'xlrd',
        'docx'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'docx':
                import docx
            else:
                __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    return missing_modules

def create_splash_screen():
    """إنشاء شاشة البداية"""
    # إنشاء صورة شاشة البداية
    pixmap = QPixmap(400, 300)
    pixmap.fill(QColor(41, 128, 185))  # لون أزرق
    
    painter = QPainter(pixmap)
    painter.setPen(QColor(255, 255, 255))
    
    # إعداد الخط العربي
    font = QFont("Arial", 16, QFont.Bold)
    painter.setFont(font)
    
    # رسم النص
    painter.drawText(pixmap.rect(), Qt.AlignCenter, 
                    "نظام تسجيل غياب الطلاب\n\n"
                    "مدرسة أبو عبيدة المتوسطة\n\n"
                    "جاري التحميل...")
    
    painter.end()
    
    return QSplashScreen(pixmap)

def setup_application():
    """إعداد التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد معلومات التطبيق
    app.setApplicationName("نظام تسجيل غياب الطلاب")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("مدرسة أبو عبيدة المتوسطة")
    
    # إعداد اتجاه النص العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي الافتراضي
    arabic_font = QFont("Tahoma", 11)
    app.setFont(arabic_font)
    
    return app

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        from data.database_manager import DatabaseManager
        from utils.config import Config

        config = Config()
        # الحصول على مسار قاعدة البيانات من الإعدادات
        db_path = config.get('database.path', 'data/attendance.db')
        db_manager = DatabaseManager(db_path)

        # التأكد من وجود قاعدة البيانات والجداول
        # قاعدة البيانات تتم تهيئتها تلقائياً عند إنشاء DatabaseManager
        with db_manager.get_connection() as conn:
            # فقط للتأكد من أن الاتصال يعمل
            pass

        return True
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للتطبيق"""
    print("=" * 50)
    print("نظام تسجيل غياب الطلاب")
    print("Student Attendance System")
    print("=" * 50)
    print("مدرسة أبو عبيدة المتوسطة")
    print("Abu Ubaida Intermediate School")
    print("=" * 50)
    print(f"تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # فحص التبعيات
    print("🔍 فحص التبعيات المطلوبة...")
    missing_modules = check_dependencies()
    
    if missing_modules:
        print("❌ التبعيات المفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\nيرجى تثبيت التبعيات المفقودة باستخدام:")
        print("pip install PySide6 pandas openpyxl xlrd python-docx")
        return 1
    
    print("✅ جميع التبعيات متوفرة")
    
    # إعداد التطبيق
    print("🚀 تهيئة التطبيق...")
    app = setup_application()
    
    # إنشاء شاشة البداية
    splash = create_splash_screen()
    splash.show()
    app.processEvents()
    
    # تهيئة قاعدة البيانات
    print("🗄️  تهيئة قاعدة البيانات...")
    splash.showMessage("تهيئة قاعدة البيانات...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
    app.processEvents()
    
    if not initialize_database():
        splash.close()
        QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
        return 1
    
    print("✅ تم تهيئة قاعدة البيانات بنجاح")
    
    # تحميل الواجهة الرئيسية
    print("🖥️  تحميل الواجهة الرئيسية...")
    splash.showMessage("تحميل الواجهة الرئيسية...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
    app.processEvents()
    
    try:
        # استخدام الواجهة المبسطة للاختبار
        from test_gui import SimpleMainWindow
        main_window = SimpleMainWindow()
        
        # إخفاء شاشة البداية وعرض النافذة الرئيسية
        def show_main_window():
            splash.close()
            main_window.show()
            print("✅ تم تشغيل التطبيق بنجاح!")
            print("📊 الواجهة الرئيسية جاهزة للاستخدام")
            print("=" * 50)
        
        # تأخير لإظهار شاشة البداية
        QTimer.singleShot(2000, show_main_window)
        
        return app.exec()
        
    except Exception as e:
        splash.close()
        print(f"❌ خطأ في تحميل الواجهة الرئيسية: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تحميل الواجهة الرئيسية:\n{e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
