# ===== المكتبات الأساسية =====

# واجهة المستخدم الرسومية
PySide6>=6.5.0

# قاعدة البيانات
sqlite3

# معالجة ملفات Excel
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0

# إنشاء ملفات Word
python-docx>=0.8.11

# تحويل التواريخ الهجرية
hijri-converter>=2.3.0

# معالجة الصور والأيقونات
Pillow>=10.0.0

# التعامل مع التواريخ والأوقات
python-dateutil>=2.8.0

# ===== مكتبات التطوير والاختبار =====

# اختبارات الوحدة
pytest>=7.0.0
pytest-qt>=4.2.0
pytest-cov>=4.0.0

# تنسيق وفحص الكود
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# فحص الأنواع
mypy>=1.0.0

# توليد الوثائق
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# ===== مكتبات مساعدة =====

# إدارة الإعدادات
configparser>=5.3.0

# السجلات المتقدمة
loguru>=0.7.0

# التشفير وكلمات المرور
bcrypt>=4.0.0

# التعامل مع JSON
jsonschema>=4.17.0

# ضغط الملفات
zipfile36>=0.1.3

# ===== مكتبات اختيارية للميزات المتقدمة =====

# إنشاء ملفات PDF (اختياري)
reportlab>=4.0.0

# رسوم بيانية وإحصائيات
matplotlib>=3.7.0
seaborn>=0.12.0

# تحسين الأداء
numba>=0.57.0

# ===== متطلبات النظام =====

# Python >= 3.8
# Qt >= 6.5
# SQLite >= 3.35

# ===== ملاحظات التثبيت =====

# للتثبيت على Windows:
# pip install -r requirements.txt

# للتثبيت على macOS:
# pip3 install -r requirements.txt

# للتثبيت على Linux:
# pip3 install -r requirements.txt

# لإنشاء بيئة افتراضية:
# python -m venv venv
# source venv/bin/activate  # على Linux/macOS
# venv\Scripts\activate     # على Windows
# pip install -r requirements.txt
