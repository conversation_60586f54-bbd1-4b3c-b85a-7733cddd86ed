"""
إدارة إعدادات التطبيق
Application configuration management
"""

import os
import json
from typing import Any, Dict, Optional
from pathlib import Path

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from utils.constants import *
    from utils.helpers import load_config, save_config, create_directory
    from utils.exceptions import ConfigurationException, ConfigurationFileException
    from utils.logger import get_logger
except ImportError:
    try:
        from src.utils.constants import *
        from src.utils.helpers import load_config, save_config, create_directory
        from src.utils.exceptions import ConfigurationException, ConfigurationFileException
        from src.utils.logger import get_logger
    except ImportError:
        # Fallback for relative imports
        from .constants import *
        from .helpers import load_config, save_config, create_directory
        from .exceptions import ConfigurationException, ConfigurationFileException
        from .logger import get_logger

logger = get_logger("Config")


class Config:
    """
    فئة إدارة الإعدادات
    Configuration management class
    """
    
    def __init__(self, config_path: str = "config/settings.json"):
        self.config_path = config_path
        self._config = {}
        self._default_config = self._get_default_config()
        self.load()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        الحصول على الإعدادات الافتراضية
        Get default configuration
        """
        return {
            "application": {
                "name": APP_NAME,
                "version": APP_VERSION,
                "author": APP_AUTHOR,
                "language": "ar",
                "direction": "rtl"
            },
            "database": {
                "path": DATABASE_PATH,
                "backup_path": BACKUP_PATH,
                "auto_backup": True,
                "backup_interval_hours": 24,
                "max_backups": 30,
                "timeout": DB_TIMEOUT,
                "check_same_thread": DB_CHECK_SAME_THREAD
            },
            "school": {
                "name": "متوسطة أبي عبيدة",
                "stage": "متوسط",
                "stage_code": "2",
                "academic_year": "1446-1447",
                "current_semester": "الأول"
            },
            "ui": {
                "theme": "default",
                "font_family": "Arial",
                "font_size": FONT_SIZES["NORMAL"],
                "window_width": WINDOW_DEFAULT_WIDTH,
                "window_height": WINDOW_DEFAULT_HEIGHT,
                "min_width": WINDOW_MIN_WIDTH,
                "min_height": WINDOW_MIN_HEIGHT,
                "language": "ar",
                "direction": "rtl"
            },
            "reports": {
                "default_template": "official",
                "date_format": "hijri",
                "export_format": "docx",
                "export_path": EXPORT_PATH,
                "show_logo": True,
                "logo_path": "src/ui/resources/icons/logo.png",
                "font_family": REPORT_SETTINGS["DEFAULT_FONT"],
                "font_size": REPORT_SETTINGS["DEFAULT_FONT_SIZE"],
                "header_font_size": REPORT_SETTINGS["HEADER_FONT_SIZE"],
                "title_font_size": REPORT_SETTINGS["TITLE_FONT_SIZE"],
                "page_margin": REPORT_SETTINGS["PAGE_MARGIN"]
            },
            "grades": GRADE_CODES,
            "logging": {
                "level": "INFO",
                "file_path": "logs/app.log",
                "max_file_size": "10MB",
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "security": {
                "enable_user_auth": False,
                "session_timeout": 3600,
                "password_min_length": 8
            },
            "data_limits": DATA_LIMITS,
            "file_types": SUPPORTED_FILE_TYPES
        }
    
    def load(self) -> bool:
        """
        تحميل الإعدادات من الملف
        Load configuration from file
        """
        try:
            if os.path.exists(self.config_path):
                self._config = load_config(self.config_path)
                # دمج الإعدادات الافتراضية مع المحملة
                self._merge_with_defaults()
                logger.info(f"تم تحميل الإعدادات من: {self.config_path}")
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self._config = self._default_config.copy()
                self.save()
                logger.info("تم إنشاء ملف الإعدادات الافتراضي")
            return True
        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")
            self._config = self._default_config.copy()
            return False
    
    def save(self) -> bool:
        """
        حفظ الإعدادات في الملف
        Save configuration to file
        """
        try:
            # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
            config_dir = os.path.dirname(self.config_path)
            if config_dir:
                create_directory(config_dir)
            
            success = save_config(self._config, self.config_path)
            if success:
                logger.info(f"تم حفظ الإعدادات في: {self.config_path}")
            else:
                logger.error("فشل في حفظ الإعدادات")
            return success
        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def _merge_with_defaults(self):
        """
        دمج الإعدادات المحملة مع الافتراضية
        Merge loaded config with defaults
        """
        def merge_dict(default: dict, loaded: dict) -> dict:
            result = default.copy()
            for key, value in loaded.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self._config = merge_dict(self._default_config, self._config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد
        Get configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        تعيين قيمة إعداد
        Set configuration value
        """
        keys = key.split('.')
        config = self._config
        
        try:
            # الانتقال إلى المستوى الأخير
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # تعيين القيمة
            config[keys[-1]] = value
            logger.debug(f"تم تعيين الإعداد: {key} = {value}")
            return True
        except Exception as e:
            logger.error(f"خطأ في تعيين الإعداد {key}: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """
        إعادة تعيين الإعدادات للقيم الافتراضية
        Reset configuration to defaults
        """
        try:
            self._config = self._default_config.copy()
            success = self.save()
            if success:
                logger.info("تم إعادة تعيين الإعدادات للقيم الافتراضية")
            return success
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين الإعدادات: {e}")
            return False
    
    def validate(self) -> bool:
        """
        التحقق من صحة الإعدادات
        Validate configuration
        """
        try:
            # التحقق من الأقسام المطلوبة
            required_sections = ["application", "database", "school", "ui", "reports"]
            for section in required_sections:
                if section not in self._config:
                    logger.error(f"قسم الإعدادات مفقود: {section}")
                    return False
            
            # التحقق من مسارات الملفات
            db_path = self.get("database.path")
            if db_path:
                db_dir = os.path.dirname(db_path)
                if db_dir and not os.path.exists(db_dir):
                    create_directory(db_dir)
            
            backup_path = self.get("database.backup_path")
            if backup_path and not os.path.exists(backup_path):
                create_directory(backup_path)
            
            export_path = self.get("reports.export_path")
            if export_path and not os.path.exists(export_path):
                create_directory(export_path)
            
            logger.info("تم التحقق من صحة الإعدادات بنجاح")
            return True
        except Exception as e:
            logger.error(f"خطأ في التحقق من الإعدادات: {e}")
            return False
    
    def get_database_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات قاعدة البيانات"""
        return self.get("database", {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات واجهة المستخدم"""
        return self.get("ui", {})
    
    def get_school_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات المدرسة"""
        return self.get("school", {})
    
    def get_reports_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات التقارير"""
        return self.get("reports", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات السجلات"""
        return self.get("logging", {})
    
    def update_school_info(self, name: str, stage: str, academic_year: str, semester: str) -> bool:
        """
        تحديث معلومات المدرسة
        Update school information
        """
        try:
            self.set("school.name", name)
            self.set("school.stage", stage)
            self.set("school.academic_year", academic_year)
            self.set("school.current_semester", semester)
            return self.save()
        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات المدرسة: {e}")
            return False
    
    def update_ui_settings(self, font_family: str, font_size: int, theme: str) -> bool:
        """
        تحديث إعدادات واجهة المستخدم
        Update UI settings
        """
        try:
            self.set("ui.font_family", font_family)
            self.set("ui.font_size", font_size)
            self.set("ui.theme", theme)
            return self.save()
        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات واجهة المستخدم: {e}")
            return False


# إنشاء مثيل عام للإعدادات
app_config = Config()


def get_config() -> Config:
    """
    الحصول على مثيل الإعدادات
    Get configuration instance
    """
    return app_config
