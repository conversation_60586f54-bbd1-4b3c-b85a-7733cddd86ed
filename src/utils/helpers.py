"""
دوال مساعدة عامة
General helper functions for the Student Attendance System
"""

import os
import json
import re
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from utils.constants import *
except ImportError:
    try:
        from src.utils.constants import *
    except ImportError:
        # Fallback for relative imports
        from .constants import *


def load_config(config_path: str = "config/settings.json") -> Dict[str, Any]:
    """
    تحميل ملف الإعدادات
    Load configuration file
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"ملف الإعدادات غير موجود: {config_path}")
        return {}
    except json.JSONDecodeError:
        print(f"خطأ في تحليل ملف الإعدادات: {config_path}")
        return {}


def save_config(config: Dict[str, Any], config_path: str = "config/settings.json") -> bool:
    """
    حفظ ملف الإعدادات
    Save configuration file
    """
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"خطأ في حفظ الإعدادات: {e}")
        return False


def validate_civil_id(civil_id: str) -> bool:
    """
    التحقق من صحة رقم الهوية المدنية
    Validate Saudi civil ID
    """
    if not civil_id:
        return False

    # تحويل إلى نص وإزالة المسافات
    civil_id = str(civil_id).strip()

    # التحقق من أن الرقم يحتوي على أرقام فقط
    if not civil_id.isdigit():
        return False

    # قبول أرقام الهوية من 8 إلى 10 أرقام (مرونة أكثر)
    if len(civil_id) < 8 or len(civil_id) > 10:
        return False

    return True


def validate_phone(phone: str) -> bool:
    """
    التحقق من صحة رقم الجوال السعودي
    Validate Saudi phone number
    """
    if not phone:
        return False
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d]', '', phone)
    
    # التحقق من الطول والبداية
    if len(phone) == 12 and phone.startswith('966'):
        return True
    elif len(phone) == 10 and phone.startswith('05'):
        return True
    elif len(phone) == 9 and phone.startswith('5'):
        return True
    
    return False


def format_phone(phone: str) -> str:
    """
    تنسيق رقم الجوال
    Format phone number
    """
    if not phone:
        return ""
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d]', '', phone)
    
    # تحويل إلى التنسيق الدولي
    if len(phone) == 10 and phone.startswith('05'):
        phone = '966' + phone[1:]
    elif len(phone) == 9 and phone.startswith('5'):
        phone = '966' + phone
    
    return phone


def validate_grade_code(grade_code: str) -> bool:
    """
    التحقق من صحة رمز الصف
    Validate grade code
    """
    return grade_code in GRADE_CODES


def get_grade_name(grade_code: str) -> str:
    """
    الحصول على اسم الصف من الرمز
    Get grade name from code
    """
    return GRADE_CODES.get(grade_code, "غير محدد")


def get_grade_level(grade_code: str) -> int:
    """
    الحصول على مستوى الصف
    Get grade level
    """
    return GRADE_LEVELS.get(grade_code, 0)


def format_date_arabic(date_obj: Union[date, datetime], format_type: str = "display") -> str:
    """
    تنسيق التاريخ بالعربية
    Format date in Arabic
    """
    if not date_obj:
        return ""
    
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()
    
    if format_type == "display":
        return date_obj.strftime("%d/%m/%Y")
    elif format_type == "db":
        return date_obj.strftime("%Y-%m-%d")
    else:
        return str(date_obj)


def get_weekday_arabic(date_obj: Union[date, datetime]) -> str:
    """
    الحصول على اسم يوم الأسبوع بالعربية
    Get weekday name in Arabic
    """
    if not date_obj:
        return ""
    
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()
    
    weekday_names = {
        0: "الاثنين",
        1: "الثلاثاء", 
        2: "الأربعاء",
        3: "الخميس",
        4: "الجمعة",
        5: "السبت",
        6: "الأحد"
    }
    
    return weekday_names.get(date_obj.weekday(), "")


def create_directory(path: str) -> bool:
    """
    إنشاء مجلد إذا لم يكن موجوداً
    Create directory if it doesn't exist
    """
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        print(f"خطأ في إنشاء المجلد {path}: {e}")
        return False


def get_file_size(file_path: str) -> int:
    """
    الحصول على حجم الملف بالبايت
    Get file size in bytes
    """
    try:
        return os.path.getsize(file_path)
    except:
        return 0


def format_file_size(size_bytes: int) -> str:
    """
    تنسيق حجم الملف
    Format file size
    """
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    تنظيف اسم الملف من الرموز غير المسموحة
    Sanitize filename
    """
    # إزالة الرموز غير المسموحة
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # إزالة المسافات الزائدة
    filename = filename.strip()
    
    # التأكد من عدم تجاوز الحد الأقصى للطول
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename


def generate_backup_filename(prefix: str = "backup") -> str:
    """
    إنشاء اسم ملف نسخة احتياطية
    Generate backup filename
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.db"


def is_valid_email(email: str) -> bool:
    """
    التحقق من صحة البريد الإلكتروني
    Validate email address
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    اقتطاع النص إذا كان طويلاً
    Truncate text if too long
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def safe_int(value: Any, default: int = 0) -> int:
    """
    تحويل آمن إلى رقم صحيح
    Safe integer conversion
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    تحويل آمن إلى رقم عشري
    Safe float conversion
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_str(value: Any, default: str = "") -> str:
    """
    تحويل آمن إلى نص
    Safe string conversion
    """
    try:
        return str(value) if value is not None else default
    except:
        return default


def calculate_percentage(part: int, total: int) -> float:
    """
    حساب النسبة المئوية
    Calculate percentage
    """
    if total == 0:
        return 0.0
    return round((part / total) * 100, 2)


def get_current_academic_year() -> str:
    """
    الحصول على العام الدراسي الحالي
    Get current academic year
    """
    now = datetime.now()
    if now.month >= 9:  # العام الدراسي يبدأ في سبتمبر
        return f"{now.year}-{now.year + 1}"
    else:
        return f"{now.year - 1}-{now.year}"


def is_weekend(date_obj: Union[date, datetime]) -> bool:
    """
    التحقق من كون التاريخ في نهاية الأسبوع (الجمعة والسبت)
    Check if date is weekend (Friday and Saturday)
    """
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()
    
    # الجمعة = 4، السبت = 5
    return date_obj.weekday() in [4, 5]
