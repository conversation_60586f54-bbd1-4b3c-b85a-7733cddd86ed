"""
نظام السجلات
Logging system for the Student Attendance System
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from typing import Optional

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from utils.constants import LOG_LEVELS
    from utils.helpers import create_directory
except ImportError:
    try:
        from src.utils.constants import LOG_LEVELS
        from src.utils.helpers import create_directory
    except ImportError:
        # Fallback for relative imports
        from .constants import LOG_LEVELS
        from .helpers import create_directory


class Logger:
    """
    فئة إدارة السجلات
    Logger management class
    """
    
    def __init__(self, name: str = "StudentAttendance", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """
        إعداد نظام السجلات
        Setup logging system
        """
        # إنشاء مجلد السجلات
        create_directory(self.log_dir)
        
        # إنشاء logger
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # تجنب إضافة handlers متعددة
        if self.logger.handlers:
            return
        
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # معالج الملف مع التدوير
        log_file = os.path.join(self.log_dir, f"{self.name}.log")
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # إضافة المعالجات
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs):
        """تسجيل رسالة تفصيلية"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """تسجيل رسالة معلومات"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """تسجيل رسالة تحذير"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """تسجيل رسالة خطأ"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """تسجيل رسالة حرجة"""
        self.logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """تسجيل استثناء مع التفاصيل"""
        self.logger.exception(message, **kwargs)
    
    def log_function_call(self, func_name: str, args: tuple = (), kwargs: dict = None):
        """
        تسجيل استدعاء دالة
        Log function call
        """
        kwargs = kwargs or {}
        self.debug(f"استدعاء الدالة: {func_name} - المعاملات: {args} - الخيارات: {kwargs}")
    
    def log_database_operation(self, operation: str, table: str, success: bool, details: str = ""):
        """
        تسجيل عملية قاعدة بيانات
        Log database operation
        """
        status = "نجح" if success else "فشل"
        message = f"عملية قاعدة البيانات: {operation} على الجدول {table} - الحالة: {status}"
        if details:
            message += f" - التفاصيل: {details}"
        
        if success:
            self.info(message)
        else:
            self.error(message)
    
    def log_user_action(self, user: str, action: str, details: str = ""):
        """
        تسجيل إجراء المستخدم
        Log user action
        """
        message = f"إجراء المستخدم: {user} - العملية: {action}"
        if details:
            message += f" - التفاصيل: {details}"
        self.info(message)
    
    def log_system_event(self, event: str, level: str = "info", details: str = ""):
        """
        تسجيل حدث النظام
        Log system event
        """
        message = f"حدث النظام: {event}"
        if details:
            message += f" - التفاصيل: {details}"
        
        if level.lower() == "debug":
            self.debug(message)
        elif level.lower() == "warning":
            self.warning(message)
        elif level.lower() == "error":
            self.error(message)
        elif level.lower() == "critical":
            self.critical(message)
        else:
            self.info(message)


# إنشاء logger عام للتطبيق
app_logger = Logger("StudentAttendance")


def get_logger(name: Optional[str] = None) -> Logger:
    """
    الحصول على logger
    Get logger instance
    """
    if name:
        return Logger(name)
    return app_logger


def log_startup():
    """تسجيل بدء تشغيل التطبيق"""
    app_logger.info("=" * 50)
    app_logger.info("بدء تشغيل برنامج تسجيل غياب الطلاب")
    app_logger.info(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    app_logger.info("=" * 50)


def log_shutdown():
    """تسجيل إغلاق التطبيق"""
    app_logger.info("=" * 50)
    app_logger.info("إغلاق برنامج تسجيل غياب الطلاب")
    app_logger.info(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    app_logger.info("=" * 50)


def log_error_with_traceback(error: Exception, context: str = ""):
    """
    تسجيل خطأ مع تفاصيل التتبع
    Log error with traceback
    """
    message = f"خطأ في التطبيق"
    if context:
        message += f" - السياق: {context}"
    message += f" - نوع الخطأ: {type(error).__name__} - الرسالة: {str(error)}"
    
    app_logger.exception(message)


# دوال مساعدة سريعة
def debug(message: str):
    """تسجيل سريع - تفصيلي"""
    app_logger.debug(message)


def info(message: str):
    """تسجيل سريع - معلومات"""
    app_logger.info(message)


def warning(message: str):
    """تسجيل سريع - تحذير"""
    app_logger.warning(message)


def error(message: str):
    """تسجيل سريع - خطأ"""
    app_logger.error(message)


def critical(message: str):
    """تسجيل سريع - حرج"""
    app_logger.critical(message)
