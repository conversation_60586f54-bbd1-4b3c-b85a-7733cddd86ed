"""
ثوابت التطبيق
Constants for the Student Attendance System
"""

# ===== معلومات التطبيق =====
APP_NAME = "برنامج تسجيل غياب الطلاب"
APP_VERSION = "1.0.0"
APP_AUTHOR = "متوسطة أبي عبيدة"

# ===== أكواد الصفوف =====
GRADE_CODES = {
    "0725": "الأول متوسط",
    "0825": "الثاني متوسط",
    "0925": "الثالث متوسط",
    # إضافة رموز الصفوف الجديدة
    "1A": "الأول متوسط - أ",
    "1B": "الأول متوسط - ب",
    "1C": "الأول متوسط - ج",
    "2A": "الثاني متوسط - أ",
    "2B": "الثاني متوسط - ب",
    "2C": "الثاني متوسط - ج",
    "3A": "الثالث متوسط - أ",
    "3B": "الثالث متوسط - ب",
    "3C": "الثالث متوسط - ج"
}

GRADE_LEVELS = {
    "0725": 1,
    "0825": 2,
    "0925": 3
}

# ===== أنواع التقارير =====
REPORT_TYPES = {
    "STUDENT": "تقرير طالب",
    "GRADE": "تقرير صف",
    "DAILY": "تقرير يومي",
    "MONTHLY": "تقرير شهري",
    "SEMESTER": "تقرير فصلي"
}

# ===== حالات الغياب =====
ABSENCE_TYPES = {
    "EXCUSED": "بعذر",
    "UNEXCUSED": "بدون عذر"
}

# ===== الفصول الدراسية =====
SEMESTERS = {
    "1": "الفصل الأول",
    "2": "الفصل الثاني"
}

# ===== الفترات =====
PERIODS = {
    "MORNING": "صباحية",
    "EVENING": "مسائية"
}

# ===== أيام الأسبوع =====
WEEKDAYS = {
    "SUNDAY": "الأحد",
    "MONDAY": "الاثنين", 
    "TUESDAY": "الثلاثاء",
    "WEDNESDAY": "الأربعاء",
    "THURSDAY": "الخميس",
    "FRIDAY": "الجمعة",
    "SATURDAY": "السبت"
}

# ===== الشهور الهجرية =====
HIJRI_MONTHS = {
    1: "محرم",
    2: "صفر",
    3: "ربيع الأول",
    4: "ربيع الثاني",
    5: "جمادى الأولى",
    6: "جمادى الثانية",
    7: "رجب",
    8: "شعبان",
    9: "رمضان",
    10: "شوال",
    11: "ذو القعدة",
    12: "ذو الحجة"
}

# ===== الشهور الميلادية =====
GREGORIAN_MONTHS = {
    1: "يناير",
    2: "فبراير",
    3: "مارس",
    4: "أبريل",
    5: "مايو",
    6: "يونيو",
    7: "يوليو",
    8: "أغسطس",
    9: "سبتمبر",
    10: "أكتوبر",
    11: "نوفمبر",
    12: "ديسمبر"
}

# ===== مسارات الملفات =====
DATABASE_PATH = "data/database.db"
BACKUP_PATH = "data/backups/"
EXPORT_PATH = "data/exports/"
LOG_PATH = "logs/"
CONFIG_PATH = "config/"

# ===== إعدادات قاعدة البيانات =====
DB_TIMEOUT = 30
DB_CHECK_SAME_THREAD = False

# ===== إعدادات الواجهة =====
WINDOW_MIN_WIDTH = 1000
WINDOW_MIN_HEIGHT = 700
WINDOW_DEFAULT_WIDTH = 1200
WINDOW_DEFAULT_HEIGHT = 800

# ===== أحجام الخطوط =====
FONT_SIZES = {
    "SMALL": 10,
    "NORMAL": 12,
    "LARGE": 14,
    "XLARGE": 16,
    "TITLE": 18
}

# ===== الألوان =====
COLORS = {
    "PRIMARY": "#2196F3",
    "SECONDARY": "#FFC107", 
    "SUCCESS": "#4CAF50",
    "WARNING": "#FF9800",
    "ERROR": "#F44336",
    "INFO": "#00BCD4",
    "LIGHT": "#F5F5F5",
    "DARK": "#212121"
}

# ===== رسائل النظام =====
MESSAGES = {
    "SUCCESS_SAVE": "تم الحفظ بنجاح",
    "SUCCESS_DELETE": "تم الحذف بنجاح",
    "SUCCESS_UPDATE": "تم التحديث بنجاح",
    "SUCCESS_IMPORT": "تم الاستيراد بنجاح",
    "SUCCESS_EXPORT": "تم التصدير بنجاح",
    "ERROR_SAVE": "خطأ في الحفظ",
    "ERROR_DELETE": "خطأ في الحذف",
    "ERROR_UPDATE": "خطأ في التحديث",
    "ERROR_IMPORT": "خطأ في الاستيراد",
    "ERROR_EXPORT": "خطأ في التصدير",
    "ERROR_CONNECTION": "خطأ في الاتصال بقاعدة البيانات",
    "ERROR_VALIDATION": "خطأ في التحقق من البيانات",
    "CONFIRM_DELETE": "هل أنت متأكد من الحذف؟",
    "CONFIRM_OVERWRITE": "هل تريد استبدال الملف الموجود؟"
}

# ===== تنسيقات التاريخ =====
DATE_FORMATS = {
    "HIJRI_DISPLAY": "%d/%m/%Y هـ",
    "GREGORIAN_DISPLAY": "%d/%m/%Y م",
    "HIJRI_DB": "%Y-%m-%d",
    "GREGORIAN_DB": "%Y-%m-%d",
    "DATETIME_DB": "%Y-%m-%d %H:%M:%S"
}

# ===== أنواع الملفات المدعومة =====
SUPPORTED_FILE_TYPES = {
    "EXCEL": [".xls", ".xlsx"],
    "WORD": [".docx"],
    "PDF": [".pdf"],
    "IMAGE": [".png", ".jpg", ".jpeg", ".gif", ".bmp"]
}

# ===== حدود البيانات =====
DATA_LIMITS = {
    "MAX_STUDENTS_PER_GRADE": 50,
    "MAX_ABSENCE_REASON_LENGTH": 500,
    "MAX_STUDENT_NAME_LENGTH": 100,
    "MAX_PHONE_LENGTH": 15,
    "MIN_CIVIL_ID_LENGTH": 10,
    "MAX_CIVIL_ID_LENGTH": 10
}

# ===== إعدادات التقارير =====
REPORT_SETTINGS = {
    "DEFAULT_FONT": "Arial",
    "DEFAULT_FONT_SIZE": 12,
    "HEADER_FONT_SIZE": 14,
    "TITLE_FONT_SIZE": 16,
    "TABLE_BORDER_WIDTH": 1,
    "PAGE_MARGIN": 2.5  # بالسنتيمتر
}

# ===== أنماط الجداول =====
TABLE_STYLES = {
    "SIMPLE": "جدول بسيط",
    "BORDERED": "جدول بحدود",
    "STRIPED": "جدول مخطط",
    "PROFESSIONAL": "جدول احترافي"
}

# ===== حالات المهام =====
TASK_STATUS = {
    "PENDING": "في الانتظار",
    "PROCESSING": "قيد المعالجة", 
    "COMPLETED": "مكتمل",
    "FAILED": "فشل"
}

# ===== مستويات السجلات =====
LOG_LEVELS = {
    "DEBUG": "تفصيلي",
    "INFO": "معلومات",
    "WARNING": "تحذير",
    "ERROR": "خطأ",
    "CRITICAL": "حرج"
}
