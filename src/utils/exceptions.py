"""
استثناءات مخصصة للتطبيق
Custom exceptions for the Student Attendance System
"""


class StudentAttendanceException(Exception):
    """
    الاستثناء الأساسي لتطبيق تسجيل الغياب
    Base exception for Student Attendance System
    """
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class DatabaseException(StudentAttendanceException):
    """
    استثناءات قاعدة البيانات
    Database related exceptions
    """
    pass


class DatabaseConnectionException(DatabaseException):
    """
    خطأ في الاتصال بقاعدة البيانات
    Database connection error
    """
    def __init__(self, message: str = "فشل الاتصال بقاعدة البيانات"):
        super().__init__(message, "DB_CONNECTION_ERROR")


class DatabaseQueryException(DatabaseException):
    """
    خطأ في استعلام قاعدة البيانات
    Database query error
    """
    def __init__(self, message: str = "خطأ في تنفيذ الاستعلام"):
        super().__init__(message, "DB_QUERY_ERROR")


class DatabaseIntegrityException(DatabaseException):
    """
    خطأ في تكامل البيانات
    Database integrity error
    """
    def __init__(self, message: str = "خطأ في تكامل البيانات"):
        super().__init__(message, "DB_INTEGRITY_ERROR")


class ValidationException(StudentAttendanceException):
    """
    استثناءات التحقق من البيانات
    Data validation exceptions
    """
    pass


class InvalidStudentDataException(ValidationException):
    """
    بيانات طالب غير صحيحة
    Invalid student data
    """
    def __init__(self, message: str = "بيانات الطالب غير صحيحة"):
        super().__init__(message, "INVALID_STUDENT_DATA")


class InvalidCivilIdException(ValidationException):
    """
    رقم هوية مدنية غير صحيح
    Invalid civil ID
    """
    def __init__(self, message: str = "رقم الهوية المدنية غير صحيح"):
        super().__init__(message, "INVALID_CIVIL_ID")


class InvalidPhoneException(ValidationException):
    """
    رقم جوال غير صحيح
    Invalid phone number
    """
    def __init__(self, message: str = "رقم الجوال غير صحيح"):
        super().__init__(message, "INVALID_PHONE")


class InvalidGradeCodeException(ValidationException):
    """
    رمز صف غير صحيح
    Invalid grade code
    """
    def __init__(self, message: str = "رمز الصف غير صحيح"):
        super().__init__(message, "INVALID_GRADE_CODE")


class InvalidDateException(ValidationException):
    """
    تاريخ غير صحيح
    Invalid date
    """
    def __init__(self, message: str = "التاريخ غير صحيح"):
        super().__init__(message, "INVALID_DATE")


class BusinessLogicException(StudentAttendanceException):
    """
    استثناءات منطق الأعمال
    Business logic exceptions
    """
    pass


class StudentNotFoundException(BusinessLogicException):
    """
    الطالب غير موجود
    Student not found
    """
    def __init__(self, student_id: str = None):
        message = f"الطالب غير موجود"
        if student_id:
            message += f": {student_id}"
        super().__init__(message, "STUDENT_NOT_FOUND")


class DuplicateStudentException(BusinessLogicException):
    """
    طالب مكرر
    Duplicate student
    """
    def __init__(self, message: str = "الطالب موجود مسبقاً"):
        super().__init__(message, "DUPLICATE_STUDENT")


class AbsenceRecordNotFoundException(BusinessLogicException):
    """
    سجل الغياب غير موجود
    Absence record not found
    """
    def __init__(self, message: str = "سجل الغياب غير موجود"):
        super().__init__(message, "ABSENCE_RECORD_NOT_FOUND")


class DuplicateAbsenceException(BusinessLogicException):
    """
    سجل غياب مكرر لنفس اليوم
    Duplicate absence record for the same day
    """
    def __init__(self, message: str = "سجل الغياب موجود مسبقاً لهذا اليوم"):
        super().__init__(message, "DUPLICATE_ABSENCE")


class FileException(StudentAttendanceException):
    """
    استثناءات الملفات
    File related exceptions
    """
    pass


class FileNotFoundException(FileException):
    """
    الملف غير موجود
    File not found
    """
    def __init__(self, file_path: str = None):
        message = "الملف غير موجود"
        if file_path:
            message += f": {file_path}"
        super().__init__(message, "FILE_NOT_FOUND")


class FilePermissionException(FileException):
    """
    لا توجد صلاحية للوصول للملف
    File permission error
    """
    def __init__(self, message: str = "لا توجد صلاحية للوصول للملف"):
        super().__init__(message, "FILE_PERMISSION_ERROR")


class InvalidFileFormatException(FileException):
    """
    تنسيق ملف غير صحيح
    Invalid file format
    """
    def __init__(self, message: str = "تنسيق الملف غير صحيح"):
        super().__init__(message, "INVALID_FILE_FORMAT")


class ImportException(FileException):
    """
    خطأ في استيراد البيانات
    Data import error
    """
    def __init__(self, message: str = "خطأ في استيراد البيانات"):
        super().__init__(message, "IMPORT_ERROR")


class ExcelImportException(FileException):
    """
    خطأ في استيراد ملف Excel
    Excel import error
    """
    def __init__(self, message: str = "خطأ في استيراد ملف Excel"):
        super().__init__(message, "EXCEL_IMPORT_ERROR")


class WordExportException(FileException):
    """
    خطأ في تصدير ملف Word
    Word export error
    """
    def __init__(self, message: str = "خطأ في تصدير ملف Word"):
        super().__init__(message, "WORD_EXPORT_ERROR")


class ReportException(StudentAttendanceException):
    """
    استثناءات التقارير
    Report related exceptions
    """
    pass


class ReportGenerationException(ReportException):
    """
    خطأ في إنشاء التقرير
    Report generation error
    """
    def __init__(self, message: str = "خطأ في إنشاء التقرير"):
        super().__init__(message, "REPORT_GENERATION_ERROR")


class InvalidReportTypeException(ReportException):
    """
    نوع تقرير غير صحيح
    Invalid report type
    """
    def __init__(self, message: str = "نوع التقرير غير صحيح"):
        super().__init__(message, "INVALID_REPORT_TYPE")


class ReportTemplateNotFoundException(ReportException):
    """
    قالب التقرير غير موجود
    Report template not found
    """
    def __init__(self, message: str = "قالب التقرير غير موجود"):
        super().__init__(message, "REPORT_TEMPLATE_NOT_FOUND")


class ConfigurationException(StudentAttendanceException):
    """
    استثناءات الإعدادات
    Configuration exceptions
    """
    pass


class InvalidConfigurationException(ConfigurationException):
    """
    إعدادات غير صحيحة
    Invalid configuration
    """
    def __init__(self, message: str = "الإعدادات غير صحيحة"):
        super().__init__(message, "INVALID_CONFIGURATION")


class ConfigurationFileException(ConfigurationException):
    """
    خطأ في ملف الإعدادات
    Configuration file error
    """
    def __init__(self, message: str = "خطأ في ملف الإعدادات"):
        super().__init__(message, "CONFIGURATION_FILE_ERROR")


class UIException(StudentAttendanceException):
    """
    استثناءات واجهة المستخدم
    User interface exceptions
    """
    pass


class WindowInitializationException(UIException):
    """
    خطأ في تهيئة النافذة
    Window initialization error
    """
    def __init__(self, message: str = "خطأ في تهيئة النافذة"):
        super().__init__(message, "WINDOW_INIT_ERROR")


class WidgetException(UIException):
    """
    خطأ في عنصر واجهة المستخدم
    Widget error
    """
    def __init__(self, message: str = "خطأ في عنصر واجهة المستخدم"):
        super().__init__(message, "WIDGET_ERROR")


# دوال مساعدة للتعامل مع الاستثناءات
def handle_exception(exception: Exception, context: str = "") -> str:
    """
    التعامل مع الاستثناءات وإرجاع رسالة مناسبة
    Handle exceptions and return appropriate message
    """
    if isinstance(exception, StudentAttendanceException):
        return exception.message
    else:
        error_message = f"خطأ غير متوقع: {str(exception)}"
        if context:
            error_message = f"{context}: {error_message}"
        return error_message


def is_critical_error(exception: Exception) -> bool:
    """
    التحقق من كون الخطأ حرجاً
    Check if error is critical
    """
    critical_exceptions = (
        DatabaseConnectionException,
        ConfigurationFileException,
        WindowInitializationException
    )
    return isinstance(exception, critical_exceptions)
