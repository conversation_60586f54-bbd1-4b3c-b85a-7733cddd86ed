"""
نافذة التقارير
Reports window for generating and viewing reports
"""

import sys
from pathlib import Path
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QDialog, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QDateEdit, QTextEdit,
    QGroupBox, QTableWidget, QTableWidgetItem, QMessageBox,
    QProgressBar, QCheckBox, QLineEdit, QFrame, QSplitter,
    QTabWidget, QScrollArea, QSpinBox
)
from PySide6.QtCore import Qt, QDate, QThread, Signal
from PySide6.QtGui import QFont, QTextDocument, QPrintDialog, QPrinter

# إضافة مسار src إلى sys.path للاستيراد
sys.path.append(str(Path(__file__).parent.parent))
from utils.config import Config
from utils.logger import get_logger
from data.database_manager import DatabaseManager
from services.absence_service import AbsenceService
from services.report_service import ReportService
from services.hijri_date_service import HijriDateService

logger = get_logger("ReportsWindow")


class ReportGenerationThread(QThread):
    """خيط منفصل لإنشاء التقارير"""
    
    progress_updated = Signal(int)
    report_completed = Signal(bool, str)
    
    def __init__(self, report_service, report_type, start_date, end_date, output_path):
        super().__init__()
        self.report_service = report_service
        self.report_type = report_type
        self.start_date = start_date
        self.end_date = end_date
        self.output_path = output_path
    
    def run(self):
        """تشغيل إنشاء التقرير"""
        try:
            self.progress_updated.emit(25)
            
            if self.report_type == "daily":
                success = self.report_service.generate_daily_report(
                    date=self.start_date,
                    output_path=self.output_path
                )
            elif self.report_type == "weekly":
                success = self.report_service.generate_weekly_report(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    output_path=self.output_path
                )
            elif self.report_type == "monthly":
                success = self.report_service.generate_monthly_report(
                    year=self.start_date.year,
                    month=self.start_date.month,
                    output_path=self.output_path
                )
            else:
                success = False
            
            self.progress_updated.emit(100)
            self.report_completed.emit(success, self.output_path if success else "فشل في إنشاء التقرير")
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            self.report_completed.emit(False, str(e))


class ReportsWindow(QDialog):
    """
    نافذة التقارير
    Reports window for generating and viewing reports
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # تهيئة الخدمات
        self.config = Config()
        self.db_manager = DatabaseManager(self.config)
        self.absence_service = AbsenceService(self.db_manager)
        self.report_service = ReportService(self.db_manager)
        self.hijri_service = HijriDateService()
        
        # متغيرات النافذة
        self.current_report_data = None
        self.report_thread = None
        
        # إعداد النافذة
        self.setup_ui()
        self.setup_connections()
        
        logger.info("تم تهيئة نافذة التقارير")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("التقارير - نظام تسجيل غياب الطلاب")
        self.setGeometry(150, 150, 1000, 700)
        
        # إعداد الخط العربي
        arabic_font = QFont("Arial", 11)
        arabic_font.setFamily("Tahoma")
        self.setFont(arabic_font)
        
        # إعداد اتجاه النص
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # تبويب إنشاء التقارير
        self.create_generate_tab()
        
        # تبويب معاينة التقارير
        self.create_preview_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("إنشاء التقرير")
        self.generate_btn.clicked.connect(self.generate_report)
        buttons_layout.addWidget(self.generate_btn)
        
        self.preview_btn = QPushButton("معاينة")
        self.preview_btn.clicked.connect(self.preview_report)
        buttons_layout.addWidget(self.preview_btn)
        
        self.export_btn = QPushButton("تصدير إلى Word")
        self.export_btn.clicked.connect(self.export_to_word)
        buttons_layout.addWidget(self.export_btn)
        
        buttons_layout.addStretch()
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def create_generate_tab(self):
        """إنشاء تبويب إنشاء التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # قسم نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QVBoxLayout(report_type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير يومي",
            "تقرير أسبوعي", 
            "تقرير شهري",
            "تقرير مخصص"
        ])
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        report_type_layout.addWidget(self.report_type_combo)
        
        layout.addWidget(report_type_group)
        
        # قسم التواريخ
        dates_group = QGroupBox("التواريخ")
        dates_layout = QGridLayout(dates_group)
        
        # التاريخ الميلادي
        dates_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.setCalendarPopup(True)
        dates_layout.addWidget(self.start_date_edit, 0, 1)
        
        dates_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        dates_layout.addWidget(self.end_date_edit, 0, 3)
        
        # التاريخ الهجري
        dates_layout.addWidget(QLabel("التاريخ الهجري:"), 1, 0)
        self.hijri_start_label = QLabel()
        dates_layout.addWidget(self.hijri_start_label, 1, 1)
        
        self.hijri_end_label = QLabel()
        dates_layout.addWidget(self.hijri_end_label, 1, 3)
        
        layout.addWidget(dates_group)
        
        # قسم الفلاتر
        filters_group = QGroupBox("الفلاتر")
        filters_layout = QGridLayout(filters_group)
        
        # فلتر الصف
        filters_layout.addWidget(QLabel("الصف:"), 0, 0)
        self.grade_filter_combo = QComboBox()
        self.grade_filter_combo.addItem("جميع الصفوف")
        filters_layout.addWidget(self.grade_filter_combo, 0, 1)
        
        # فلتر نوع الغياب
        filters_layout.addWidget(QLabel("نوع الغياب:"), 0, 2)
        self.absence_type_combo = QComboBox()
        self.absence_type_combo.addItems([
            "جميع الأنواع",
            "غياب",
            "غياب بعذر", 
            "تأخير",
            "انصراف مبكر"
        ])
        filters_layout.addWidget(self.absence_type_combo, 0, 3)
        
        # خيارات إضافية
        filters_layout.addWidget(QLabel("تضمين:"), 1, 0)
        self.include_hijri_checkbox = QCheckBox("التاريخ الهجري")
        self.include_hijri_checkbox.setChecked(True)
        filters_layout.addWidget(self.include_hijri_checkbox, 1, 1)
        
        self.include_statistics_checkbox = QCheckBox("الإحصائيات التفصيلية")
        self.include_statistics_checkbox.setChecked(True)
        filters_layout.addWidget(self.include_statistics_checkbox, 1, 2)
        
        self.include_charts_checkbox = QCheckBox("الرسوم البيانية")
        filters_layout.addWidget(self.include_charts_checkbox, 1, 3)
        
        layout.addWidget(filters_group)
        
        # قسم إعدادات التصدير
        export_group = QGroupBox("إعدادات التصدير")
        export_layout = QGridLayout(export_group)
        
        export_layout.addWidget(QLabel("مسار الحفظ:"), 0, 0)
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setText("reports/")
        export_layout.addWidget(self.output_path_edit, 0, 1, 1, 2)
        
        browse_btn = QPushButton("تصفح...")
        browse_btn.clicked.connect(self.browse_output_path)
        export_layout.addWidget(browse_btn, 0, 3)
        
        export_layout.addWidget(QLabel("اسم الملف:"), 1, 0)
        self.filename_edit = QLineEdit()
        self.filename_edit.setText(f"تقرير_غياب_{date.today().strftime('%Y-%m-%d')}")
        export_layout.addWidget(self.filename_edit, 1, 1, 1, 3)
        
        layout.addWidget(export_group)
        
        layout.addStretch()
        
        self.tabs.addTab(tab, "إنشاء التقرير")
        
        # تحديث التواريخ الهجرية
        self.update_hijri_dates()
        
        # تحميل قائمة الصفوف
        self.load_grades()
    
    def create_preview_tab(self):
        """إنشاء تبويب معاينة التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # منطقة المعاينة
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        self.tabs.addTab(tab, "معاينة التقرير")
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول الإحصائيات
        self.statistics_table = QTableWidget()
        layout.addWidget(self.statistics_table)
        
        self.tabs.addTab(tab, "الإحصائيات")
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.start_date_edit.dateChanged.connect(self.update_hijri_dates)
        self.end_date_edit.dateChanged.connect(self.update_hijri_dates)
    
    def on_report_type_changed(self, report_type):
        """عند تغيير نوع التقرير"""
        if report_type == "تقرير يومي":
            self.end_date_edit.setEnabled(False)
            self.end_date_edit.setDate(self.start_date_edit.date())
        elif report_type == "تقرير أسبوعي":
            self.end_date_edit.setEnabled(True)
            # تعيين نهاية الأسبوع
            start_date = self.start_date_edit.date().toPython()
            end_date = start_date + timedelta(days=6)
            self.end_date_edit.setDate(QDate.fromString(end_date.isoformat(), Qt.ISODate))
        elif report_type == "تقرير شهري":
            self.end_date_edit.setEnabled(False)
            # تعيين نهاية الشهر
            start_date = self.start_date_edit.date().toPython()
            if start_date.month == 12:
                end_date = date(start_date.year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(start_date.year, start_date.month + 1, 1) - timedelta(days=1)
            self.end_date_edit.setDate(QDate.fromString(end_date.isoformat(), Qt.ISODate))
        else:  # تقرير مخصص
            self.end_date_edit.setEnabled(True)
    
    def update_hijri_dates(self):
        """تحديث التواريخ الهجرية"""
        try:
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            hijri_start = self.hijri_service.gregorian_to_hijri(start_date)
            hijri_end = self.hijri_service.gregorian_to_hijri(end_date)
            
            self.hijri_start_label.setText(hijri_start)
            self.hijri_end_label.setText(hijri_end)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث التواريخ الهجرية: {e}")
    
    def load_grades(self):
        """تحميل قائمة الصفوف"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT DISTINCT grade_code FROM students WHERE is_active = 1 ORDER BY grade_code")
            grades = cursor.fetchall()
            
            self.grade_filter_combo.clear()
            self.grade_filter_combo.addItem("جميع الصفوف")
            
            for grade in grades:
                self.grade_filter_combo.addItem(grade[0])
            
            conn.close()
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الصفوف: {e}")
    
    def browse_output_path(self):
        """تصفح مسار الحفظ"""
        from PySide6.QtWidgets import QFileDialog
        
        path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد الحفظ",
            self.output_path_edit.text()
        )
        
        if path:
            self.output_path_edit.setText(path)
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من البيانات
            if not self.validate_inputs():
                return
            
            # إعداد معاملات التقرير
            report_type_text = self.report_type_combo.currentText()
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            # تحديد نوع التقرير
            if report_type_text == "تقرير يومي":
                report_type = "daily"
            elif report_type_text == "تقرير أسبوعي":
                report_type = "weekly"
            elif report_type_text == "تقرير شهري":
                report_type = "monthly"
            else:
                report_type = "custom"
            
            # إعداد مسار الحفظ
            output_dir = Path(self.output_path_edit.text())
            output_dir.mkdir(exist_ok=True)
            
            filename = self.filename_edit.text()
            if not filename.endswith('.docx'):
                filename += '.docx'
            
            output_path = output_dir / filename
            
            # بدء إنشاء التقرير في خيط منفصل
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            
            self.generate_btn.setEnabled(False)
            
            self.report_thread = ReportGenerationThread(
                self.report_service,
                report_type,
                start_date,
                end_date,
                str(output_path)
            )
            
            self.report_thread.progress_updated.connect(self.progress_bar.setValue)
            self.report_thread.report_completed.connect(self.on_report_completed)
            self.report_thread.start()
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if self.start_date_edit.date() > self.end_date_edit.date():
            QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return False
        
        if not self.filename_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الملف")
            return False
        
        return True
    
    def on_report_completed(self, success, message):
        """عند اكتمال إنشاء التقرير"""
        self.progress_bar.setVisible(False)
        self.generate_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(
                self,
                "تم إنشاء التقرير",
                f"تم إنشاء التقرير بنجاح:\n{message}"
            )
        else:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء التقرير:\n{message}"
            )
    
    def preview_report(self):
        """معاينة التقرير"""
        try:
            # إنشاء معاينة نصية للتقرير
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            # الحصول على بيانات الغياب
            absences = self.absence_service.get_absences_by_date_range(start_date, end_date)
            
            # إنشاء نص المعاينة
            preview_text = self.create_preview_text(absences, start_date, end_date)
            
            # عرض المعاينة
            self.preview_text.setHtml(preview_text)
            self.tabs.setCurrentIndex(1)  # التبديل إلى تبويب المعاينة
            
        except Exception as e:
            logger.error(f"خطأ في معاينة التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في معاينة التقرير: {e}")
    
    def create_preview_text(self, absences, start_date, end_date):
        """إنشاء نص المعاينة"""
        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: 'Tahoma', Arial, sans-serif; direction: rtl; }}
                h1, h2 {{ text-align: center; color: #2c3e50; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                th {{ background-color: #f2f2f2; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .statistics {{ background-color: #f8f9fa; padding: 15px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>مدرسة أبو عبيدة المتوسطة</h1>
                <h2>تقرير الغياب</h2>
                <p>من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}</p>
                <p>التاريخ الهجري: {self.hijri_service.gregorian_to_hijri(start_date)} - {self.hijri_service.gregorian_to_hijri(end_date)}</p>
            </div>
        """
        
        if absences:
            # إحصائيات عامة
            total_absences = len(absences)
            excused_absences = len([a for a in absences if a.get('is_excused', False)])
            unexcused_absences = total_absences - excused_absences
            
            html += f"""
            <div class="statistics">
                <h3>الإحصائيات العامة</h3>
                <p>إجمالي الغيابات: {total_absences}</p>
                <p>الغيابات بعذر: {excused_absences}</p>
                <p>الغيابات بدون عذر: {unexcused_absences}</p>
            </div>
            
            <h3>تفاصيل الغيابات</h3>
            <table>
                <tr>
                    <th>التاريخ</th>
                    <th>اسم الطالب</th>
                    <th>الصف</th>
                    <th>نوع الغياب</th>
                    <th>السبب</th>
                </tr>
            """
            
            for absence in absences:
                html += f"""
                <tr>
                    <td>{absence.get('absence_date', '')}</td>
                    <td>{absence.get('student_name', '')}</td>
                    <td>{absence.get('grade_code', '')}</td>
                    <td>{absence.get('absence_type', '')}</td>
                    <td>{absence.get('reason', '') or '-'}</td>
                </tr>
                """
            
            html += "</table>"
        else:
            html += "<p style='text-align: center; color: #666;'>لا توجد غيابات في الفترة المحددة</p>"
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def export_to_word(self):
        """تصدير إلى Word"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "يرجى إنشاء التقرير أولاً")
            return
        
        self.generate_report()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.report_thread and self.report_thread.isRunning():
            self.report_thread.quit()
            self.report_thread.wait()
        
        event.accept()


def main():
    """تشغيل نافذة التقارير للاختبار"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ReportsWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
