"""
نافذة الإعدادات
Settings window for application configuration and data management
"""

import sys
import shutil
from pathlib import Path
from datetime import datetime, date
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QDialog, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QLineEdit, QSpinBox,
    QGroupBox, QCheckBox, QTextEdit, QMessageBox,
    QProgressBar, QFileDialog, QTabWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

# إضافة مسار src إلى sys.path للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from utils.config import Config
    from utils.logger import get_logger
    from data.database_manager import DatabaseManager
    from services.excel_import_service import ExcelImportService
except ImportError:
    # Fallback for different import paths
    from src.utils.config import Config
    from src.utils.logger import get_logger
    from src.data.database_manager import DatabaseManager
    from src.services.excel_import_service import ExcelImportService

logger = get_logger("SettingsWindow")


class BackupThread(QThread):
    """خيط منفصل لعمليات النسخ الاحتياطي"""

    progress_updated = Signal(int)
    backup_completed = Signal(bool, str)

    def __init__(self, source_path, backup_path):
        super().__init__()
        self.source_path = source_path
        self.backup_path = backup_path

    def run(self):
        """تشغيل النسخ الاحتياطي"""
        try:
            self.progress_updated.emit(25)

            # إنشاء مجلد النسخ الاحتياطي
            backup_dir = Path(self.backup_path)
            backup_dir.mkdir(parents=True, exist_ok=True)

            self.progress_updated.emit(50)

            # نسخ قاعدة البيانات
            source_db = Path(self.source_path)
            if source_db.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = backup_dir / f"attendance_backup_{timestamp}.db"
                shutil.copy2(source_db, backup_file)

                self.progress_updated.emit(100)
                self.backup_completed.emit(True, str(backup_file))
            else:
                self.backup_completed.emit(False, "ملف قاعدة البيانات غير موجود")

        except Exception as e:
            logger.error(f"خطأ في النسخ الاحتياطي: {e}")
            self.backup_completed.emit(False, str(e))


class SettingsWindow(QDialog):
    """
    نافذة الإعدادات
    Settings window for application configuration and data management
    """

    def __init__(self, parent=None):
        super().__init__(parent)

        # تهيئة الخدمات
        self.config = Config()
        self.db_manager = DatabaseManager()  # DatabaseManager سيحصل على المسار من Config تلقائياً
        self.import_service = ExcelImportService(self.db_manager)

        # متغيرات النافذة
        self.backup_thread = None

        # إعداد النافذة
        self.setup_ui()
        self.load_current_settings()

        logger.info("تم تهيئة نافذة الإعدادات")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الإعدادات - نظام تسجيل غياب الطلاب")
        self.setGeometry(200, 200, 800, 600)

        # إعداد الخط العربي
        arabic_font = QFont("Arial", 11)
        arabic_font.setFamily("Tahoma")
        self.setFont(arabic_font)

        # إعداد اتجاه النص
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # إنشاء التبويبات
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # تبويب الإعدادات العامة
        self.create_general_tab()

        # تبويب إدارة البيانات
        self.create_data_management_tab()

        # تبويب النسخ الاحتياطي
        self.create_backup_tab()

        # تبويب معلومات المدرسة
        self.create_school_info_tab()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ الإعدادات")
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)

        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_btn)

        buttons_layout.addStretch()

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        main_layout.addLayout(buttons_layout)

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات اللغة والعرض
        display_group = QGroupBox("إعدادات العرض")
        display_layout = QGridLayout(display_group)

        display_layout.addWidget(QLabel("اللغة:"), 0, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        display_layout.addWidget(self.language_combo, 0, 1)

        display_layout.addWidget(QLabel("حجم الخط:"), 0, 2)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(11)
        display_layout.addWidget(self.font_size_spin, 0, 3)

        display_layout.addWidget(QLabel("نوع الخط:"), 1, 0)
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["Tahoma", "Arial", "Times New Roman"])
        display_layout.addWidget(self.font_family_combo, 1, 1)

        self.show_hijri_checkbox = QCheckBox("عرض التاريخ الهجري")
        self.show_hijri_checkbox.setChecked(True)
        display_layout.addWidget(self.show_hijri_checkbox, 1, 2, 1, 2)

        layout.addWidget(display_group)

        # إعدادات التقارير
        reports_group = QGroupBox("إعدادات التقارير")
        reports_layout = QGridLayout(reports_group)

        reports_layout.addWidget(QLabel("مجلد التقارير:"), 0, 0)
        self.reports_path_edit = QLineEdit()
        self.reports_path_edit.setText("reports/")
        reports_layout.addWidget(self.reports_path_edit, 0, 1, 1, 2)

        browse_reports_btn = QPushButton("تصفح...")
        browse_reports_btn.clicked.connect(self.browse_reports_path)
        reports_layout.addWidget(browse_reports_btn, 0, 3)

        reports_layout.addWidget(QLabel("تنسيق التاريخ:"), 1, 0)
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "YYYY/MM/DD",
            "DD/MM/YYYY",
            "MM/DD/YYYY"
        ])
        reports_layout.addWidget(self.date_format_combo, 1, 1)

        self.auto_backup_checkbox = QCheckBox("نسخ احتياطي تلقائي")
        reports_layout.addWidget(self.auto_backup_checkbox, 1, 2, 1, 2)

        layout.addWidget(reports_group)

        # إعدادات الإشعارات
        notifications_group = QGroupBox("الإشعارات")
        notifications_layout = QVBoxLayout(notifications_group)

        self.show_notifications_checkbox = QCheckBox("عرض الإشعارات")
        self.show_notifications_checkbox.setChecked(True)
        notifications_layout.addWidget(self.show_notifications_checkbox)

        self.sound_notifications_checkbox = QCheckBox("الإشعارات الصوتية")
        notifications_layout.addWidget(self.sound_notifications_checkbox)

        self.daily_reminder_checkbox = QCheckBox("تذكير يومي لتسجيل الغياب")
        notifications_layout.addWidget(self.daily_reminder_checkbox)

        layout.addWidget(notifications_group)

        layout.addStretch()

        self.tabs.addTab(tab, "الإعدادات العامة")

    def create_data_management_tab(self):
        """إنشاء تبويب إدارة البيانات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # استيراد البيانات
        import_group = QGroupBox("استيراد البيانات")
        import_layout = QVBoxLayout(import_group)

        import_info = QLabel(
            "يمكنك استيراد بيانات الطلاب من ملفات Excel.\n"
            "تأكد من أن الملف يحتوي على الأعمدة المطلوبة: الاسم، رقم الهوية، الصف"
        )
        import_info.setWordWrap(True)
        import_layout.addWidget(import_info)

        import_buttons_layout = QHBoxLayout()

        import_excel_btn = QPushButton("استيراد من Excel")
        import_excel_btn.clicked.connect(self.import_from_excel)
        import_buttons_layout.addWidget(import_excel_btn)

        download_template_btn = QPushButton("تحميل قالب Excel")
        download_template_btn.clicked.connect(self.download_excel_template)
        import_buttons_layout.addWidget(download_template_btn)

        import_buttons_layout.addStretch()
        import_layout.addLayout(import_buttons_layout)

        layout.addWidget(import_group)

        # إدارة قاعدة البيانات
        database_group = QGroupBox("إدارة قاعدة البيانات")
        database_layout = QVBoxLayout(database_group)

        db_info = QLabel(
            f"مسار قاعدة البيانات: {self.config.get('database.path', 'data/attendance.db')}\n"
            f"حجم قاعدة البيانات: {self.get_database_size()}"
        )
        db_info.setWordWrap(True)
        database_layout.addWidget(db_info)

        db_buttons_layout = QHBoxLayout()

        optimize_db_btn = QPushButton("تحسين قاعدة البيانات")
        optimize_db_btn.clicked.connect(self.optimize_database)
        db_buttons_layout.addWidget(optimize_db_btn)

        repair_db_btn = QPushButton("إصلاح قاعدة البيانات")
        repair_db_btn.clicked.connect(self.repair_database)
        db_buttons_layout.addWidget(repair_db_btn)

        db_buttons_layout.addStretch()
        database_layout.addLayout(db_buttons_layout)

        layout.addWidget(database_group)

        # إحصائيات البيانات
        stats_group = QGroupBox("إحصائيات البيانات")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["البيان", "العدد"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(self.stats_table)

        refresh_stats_btn = QPushButton("تحديث الإحصائيات")
        refresh_stats_btn.clicked.connect(self.refresh_statistics)
        stats_layout.addWidget(refresh_stats_btn)

        layout.addWidget(stats_group)

        layout.addStretch()

        self.tabs.addTab(tab, "إدارة البيانات")

        # تحديث الإحصائيات
        self.refresh_statistics()

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات النسخ الاحتياطي
        backup_settings_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_settings_layout = QGridLayout(backup_settings_group)

        backup_settings_layout.addWidget(QLabel("مجلد النسخ الاحتياطي:"), 0, 0)
        self.backup_path_edit = QLineEdit()
        self.backup_path_edit.setText("backups/")
        backup_settings_layout.addWidget(self.backup_path_edit, 0, 1, 1, 2)

        browse_backup_btn = QPushButton("تصفح...")
        browse_backup_btn.clicked.connect(self.browse_backup_path)
        backup_settings_layout.addWidget(browse_backup_btn, 0, 3)

        backup_settings_layout.addWidget(QLabel("تكرار النسخ التلقائي:"), 1, 0)
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems([
            "يومياً",
            "أسبوعياً",
            "شهرياً",
            "يدوياً فقط"
        ])
        backup_settings_layout.addWidget(self.backup_frequency_combo, 1, 1)

        backup_settings_layout.addWidget(QLabel("عدد النسخ المحفوظة:"), 1, 2)
        self.backup_count_spin = QSpinBox()
        self.backup_count_spin.setRange(1, 50)
        self.backup_count_spin.setValue(10)
        backup_settings_layout.addWidget(self.backup_count_spin, 1, 3)

        layout.addWidget(backup_settings_group)

        # عمليات النسخ الاحتياطي
        backup_operations_group = QGroupBox("عمليات النسخ الاحتياطي")
        backup_operations_layout = QVBoxLayout(backup_operations_group)

        backup_buttons_layout = QHBoxLayout()

        create_backup_btn = QPushButton("إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_backup)
        backup_buttons_layout.addWidget(create_backup_btn)

        restore_backup_btn = QPushButton("استعادة نسخة احتياطية")
        restore_backup_btn.clicked.connect(self.restore_backup)
        backup_buttons_layout.addWidget(restore_backup_btn)

        backup_buttons_layout.addStretch()
        backup_operations_layout.addLayout(backup_buttons_layout)

        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        backup_operations_layout.addWidget(self.backup_progress)

        layout.addWidget(backup_operations_group)

        layout.addStretch()

        self.tabs.addTab(tab, "النسخ الاحتياطي")

    def create_school_info_tab(self):
        """إنشاء تبويب معلومات المدرسة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # معلومات المدرسة الأساسية
        school_info_group = QGroupBox("معلومات المدرسة")
        school_info_layout = QGridLayout(school_info_group)

        school_info_layout.addWidget(QLabel("اسم المدرسة:"), 0, 0)
        self.school_name_edit = QLineEdit()
        self.school_name_edit.setText("مدرسة أبو عبيدة المتوسطة")
        school_info_layout.addWidget(self.school_name_edit, 0, 1, 1, 3)

        school_info_layout.addWidget(QLabel("رمز المدرسة:"), 1, 0)
        self.school_code_edit = QLineEdit()
        self.school_code_edit.setText("AUM001")
        school_info_layout.addWidget(self.school_code_edit, 1, 1)

        school_info_layout.addWidget(QLabel("المنطقة التعليمية:"), 1, 2)
        self.education_zone_edit = QLineEdit()
        self.education_zone_edit.setText("منطقة الرياض")
        school_info_layout.addWidget(self.education_zone_edit, 1, 3)

        school_info_layout.addWidget(QLabel("العنوان:"), 2, 0)
        self.school_address_edit = QTextEdit()
        self.school_address_edit.setMaximumHeight(80)
        self.school_address_edit.setText("الرياض، المملكة العربية السعودية")
        school_info_layout.addWidget(self.school_address_edit, 2, 1, 1, 3)

        school_info_layout.addWidget(QLabel("الهاتف:"), 3, 0)
        self.school_phone_edit = QLineEdit()
        self.school_phone_edit.setText("011-1234567")
        school_info_layout.addWidget(self.school_phone_edit, 3, 1)

        school_info_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 2)
        self.school_email_edit = QLineEdit()
        self.school_email_edit.setText("<EMAIL>")
        school_info_layout.addWidget(self.school_email_edit, 3, 3)

        layout.addWidget(school_info_group)

        # معلومات إدارية
        admin_info_group = QGroupBox("المعلومات الإدارية")
        admin_info_layout = QGridLayout(admin_info_group)

        admin_info_layout.addWidget(QLabel("مدير المدرسة:"), 0, 0)
        self.principal_name_edit = QLineEdit()
        self.principal_name_edit.setText("أ. محمد بن عبدالله العلي")
        admin_info_layout.addWidget(self.principal_name_edit, 0, 1, 1, 3)

        admin_info_layout.addWidget(QLabel("وكيل المدرسة:"), 1, 0)
        self.vice_principal_edit = QLineEdit()
        self.vice_principal_edit.setText("أ. أحمد بن سالم الخالد")
        admin_info_layout.addWidget(self.vice_principal_edit, 1, 1, 1, 3)

        admin_info_layout.addWidget(QLabel("المرشد الطلابي:"), 2, 0)
        self.counselor_edit = QLineEdit()
        self.counselor_edit.setText("أ. عبدالرحمن بن فهد القحطاني")
        admin_info_layout.addWidget(self.counselor_edit, 2, 1, 1, 3)

        layout.addWidget(admin_info_group)

        layout.addStretch()

        self.tabs.addTab(tab, "معلومات المدرسة")

    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # تحميل إعدادات العرض
            self.language_combo.setCurrentText(self.config.get('ui.language', 'العربية'))
            self.font_size_spin.setValue(self.config.get('ui.font_size', 11))
            self.font_family_combo.setCurrentText(self.config.get('ui.font_family', 'Tahoma'))
            self.show_hijri_checkbox.setChecked(self.config.get('ui.show_hijri', True))

            # تحميل إعدادات التقارير
            self.reports_path_edit.setText(self.config.get('reports.output_path', 'reports/'))
            self.date_format_combo.setCurrentText(self.config.get('reports.date_format', 'YYYY/MM/DD'))
            self.auto_backup_checkbox.setChecked(self.config.get('backup.auto_backup', False))

            # تحميل إعدادات الإشعارات
            self.show_notifications_checkbox.setChecked(self.config.get('notifications.enabled', True))
            self.sound_notifications_checkbox.setChecked(self.config.get('notifications.sound', False))
            self.daily_reminder_checkbox.setChecked(self.config.get('notifications.daily_reminder', False))

            # تحميل إعدادات النسخ الاحتياطي
            self.backup_path_edit.setText(self.config.get('backup.path', 'backups/'))
            self.backup_frequency_combo.setCurrentText(self.config.get('backup.frequency', 'أسبوعياً'))
            self.backup_count_spin.setValue(self.config.get('backup.keep_count', 10))

            # تحميل معلومات المدرسة
            self.school_name_edit.setText(self.config.get('school.name', 'مدرسة أبو عبيدة المتوسطة'))
            self.school_code_edit.setText(self.config.get('school.code', 'AUM001'))
            self.education_zone_edit.setText(self.config.get('school.education_zone', 'منطقة الرياض'))
            self.school_address_edit.setText(self.config.get('school.address', 'الرياض، المملكة العربية السعودية'))
            self.school_phone_edit.setText(self.config.get('school.phone', '011-1234567'))
            self.school_email_edit.setText(self.config.get('school.email', '<EMAIL>'))
            self.principal_name_edit.setText(self.config.get('school.principal', 'أ. محمد بن عبدالله العلي'))
            self.vice_principal_edit.setText(self.config.get('school.vice_principal', 'أ. أحمد بن سالم الخالد'))
            self.counselor_edit.setText(self.config.get('school.counselor', 'أ. عبدالرحمن بن فهد القحطاني'))

        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات العرض
            self.config.set('ui.language', self.language_combo.currentText())
            self.config.set('ui.font_size', self.font_size_spin.value())
            self.config.set('ui.font_family', self.font_family_combo.currentText())
            self.config.set('ui.show_hijri', self.show_hijri_checkbox.isChecked())

            # حفظ إعدادات التقارير
            self.config.set('reports.output_path', self.reports_path_edit.text())
            self.config.set('reports.date_format', self.date_format_combo.currentText())
            self.config.set('backup.auto_backup', self.auto_backup_checkbox.isChecked())

            # حفظ إعدادات الإشعارات
            self.config.set('notifications.enabled', self.show_notifications_checkbox.isChecked())
            self.config.set('notifications.sound', self.sound_notifications_checkbox.isChecked())
            self.config.set('notifications.daily_reminder', self.daily_reminder_checkbox.isChecked())

            # حفظ إعدادات النسخ الاحتياطي
            self.config.set('backup.path', self.backup_path_edit.text())
            self.config.set('backup.frequency', self.backup_frequency_combo.currentText())
            self.config.set('backup.keep_count', self.backup_count_spin.value())

            # حفظ معلومات المدرسة
            self.config.set('school.name', self.school_name_edit.text())
            self.config.set('school.code', self.school_code_edit.text())
            self.config.set('school.education_zone', self.education_zone_edit.text())
            self.config.set('school.address', self.school_address_edit.toPlainText())
            self.config.set('school.phone', self.school_phone_edit.text())
            self.config.set('school.email', self.school_email_edit.text())
            self.config.set('school.principal', self.principal_name_edit.text())
            self.config.set('school.vice_principal', self.vice_principal_edit.text())
            self.config.set('school.counselor', self.counselor_edit.text())

            # حفظ الإعدادات في الملف
            self.config.save()

            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, "تم الحفظ", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {e}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        from PySide6.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self,
            "إعادة تعيين",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات إلى القيم الافتراضية
                self.config.reset_to_defaults()
                self.load_current_settings()

                QMessageBox.information(self, "تم إعادة التعيين", "تم إعادة تعيين الإعدادات بنجاح")

            except Exception as e:
                logger.error(f"خطأ في إعادة تعيين الإعدادات: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في إعادة تعيين الإعدادات: {e}")

    def browse_reports_path(self):
        """تصفح مجلد التقارير"""
        from PySide6.QtWidgets import QFileDialog

        path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد التقارير",
            self.reports_path_edit.text()
        )

        if path:
            self.reports_path_edit.setText(path)

    def browse_backup_path(self):
        """تصفح مجلد النسخ الاحتياطي"""
        from PySide6.QtWidgets import QFileDialog

        path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد النسخ الاحتياطي",
            self.backup_path_edit.text()
        )

        if path:
            self.backup_path_edit.setText(path)

    def get_database_size(self):
        """الحصول على حجم قاعدة البيانات"""
        try:
            db_path = Path(self.config.get('database.path', 'data/attendance.db'))
            if db_path.exists():
                size_bytes = db_path.stat().st_size
                if size_bytes < 1024:
                    return f"{size_bytes} بايت"
                elif size_bytes < 1024 * 1024:
                    return f"{size_bytes / 1024:.1f} كيلوبايت"
                else:
                    return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
            else:
                return "غير موجود"
        except Exception as e:
            logger.error(f"خطأ في حساب حجم قاعدة البيانات: {e}")
            return "غير معروف"

    def refresh_statistics(self):
        """تحديث إحصائيات البيانات"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # إحصائيات مختلفة
                stats = []

                # عدد الطلاب
                cursor.execute("SELECT COUNT(*) FROM students WHERE is_active = 1")
                active_students = cursor.fetchone()[0]
                stats.append(("الطلاب النشطون", str(active_students)))

                cursor.execute("SELECT COUNT(*) FROM students")
                total_students = cursor.fetchone()[0]
                stats.append(("إجمالي الطلاب", str(total_students)))

                # عدد الصفوف
                cursor.execute("SELECT COUNT(DISTINCT grade_code) FROM students WHERE is_active = 1")
                grades_count = cursor.fetchone()[0]
                stats.append(("عدد الصفوف", str(grades_count)))

                # إحصائيات الغياب
                cursor.execute("SELECT COUNT(*) FROM absence_records")
                total_absences = cursor.fetchone()[0]
                stats.append(("إجمالي الغيابات", str(total_absences)))

                cursor.execute("SELECT COUNT(*) FROM absence_records WHERE is_excused = 1")
                excused_absences = cursor.fetchone()[0]
                stats.append(("الغيابات بعذر", str(excused_absences)))

                # آخر تحديث
                cursor.execute("SELECT MAX(created_at) FROM absence_records")
                last_update = cursor.fetchone()[0]
                if last_update:
                    stats.append(("آخر تسجيل غياب", last_update.split()[0]))
                else:
                    stats.append(("آخر تسجيل غياب", "لا يوجد"))

            # تحديث الجدول
            self.stats_table.setRowCount(len(stats))
            for row, (label, value) in enumerate(stats):
                self.stats_table.setItem(row, 0, QTableWidgetItem(label))
                self.stats_table.setItem(row, 1, QTableWidgetItem(value))

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")

    def import_from_excel(self):
        """استيراد البيانات من Excel"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف Excel",
            "",
            "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            try:
                # استيراد البيانات
                result = self.import_service.import_students_from_excel(file_path)

                if result['success']:
                    QMessageBox.information(
                        self,
                        "تم الاستيراد",
                        f"تم استيراد {result['imported_count']} طالب بنجاح"
                    )
                    self.refresh_statistics()
                else:
                    QMessageBox.warning(
                        self,
                        "فشل الاستيراد",
                        f"فشل في استيراد البيانات: {result['error']}"
                    )

            except Exception as e:
                logger.error(f"خطأ في استيراد البيانات: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في استيراد البيانات: {e}")

    def download_excel_template(self):
        """تحميل قالب Excel"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ قالب Excel",
            "قالب_بيانات_الطلاب.xlsx",
            "Excel Files (*.xlsx)"
        )

        if file_path:
            try:
                # إنشاء قالب Excel
                self.import_service.create_excel_template(file_path)

                QMessageBox.information(
                    self,
                    "تم الحفظ",
                    f"تم حفظ قالب Excel في:\n{file_path}"
                )

            except Exception as e:
                logger.error(f"خطأ في إنشاء قالب Excel: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء قالب Excel: {e}")

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        from PySide6.QtWidgets import QMessageBox

        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # تشغيل أوامر التحسين
                cursor.execute("VACUUM")
                cursor.execute("ANALYZE")

            QMessageBox.information(self, "تم التحسين", "تم تحسين قاعدة البيانات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحسين قاعدة البيانات: {e}")

    def repair_database(self):
        """إصلاح قاعدة البيانات"""
        from PySide6.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self,
            "إصلاح قاعدة البيانات",
            "هل أنت متأكد من إصلاح قاعدة البيانات؟\nسيتم إنشاء نسخة احتياطية أولاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إنشاء نسخة احتياطية أولاً
                self.create_backup()

                # إصلاح قاعدة البيانات
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()

                    cursor.execute("PRAGMA integrity_check")
                    result = cursor.fetchone()[0]

                    if result == "ok":
                        QMessageBox.information(self, "قاعدة البيانات سليمة", "قاعدة البيانات لا تحتاج إلى إصلاح")
                    else:
                        # محاولة الإصلاح
                        cursor.execute("REINDEX")
                        QMessageBox.information(self, "تم الإصلاح", "تم إصلاح قاعدة البيانات")

            except Exception as e:
                logger.error(f"خطأ في إصلاح قاعدة البيانات: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في إصلاح قاعدة البيانات: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            db_path = self.config.get('database.path', 'data/attendance.db')
            backup_path = self.backup_path_edit.text()

            self.backup_progress.setVisible(True)
            self.backup_progress.setRange(0, 100)
            self.backup_progress.setValue(0)

            self.backup_thread = BackupThread(db_path, backup_path)
            self.backup_thread.progress_updated.connect(self.backup_progress.setValue)
            self.backup_thread.backup_completed.connect(self.on_backup_completed)
            self.backup_thread.start()

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {e}")

    def on_backup_completed(self, success, message):
        """عند اكتمال النسخ الاحتياطي"""
        self.backup_progress.setVisible(False)

        from PySide6.QtWidgets import QMessageBox

        if success:
            QMessageBox.information(
                self,
                "تم إنشاء النسخة الاحتياطية",
                f"تم إنشاء النسخة الاحتياطية بنجاح:\n{message}"
            )
        else:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء النسخة الاحتياطية:\n{message}"
            )

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            self.backup_path_edit.text(),
            "Database Files (*.db)"
        )

        if file_path:
            reply = QMessageBox.question(
                self,
                "استعادة النسخة الاحتياطية",
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # نسخ ملف النسخة الاحتياطية
                    current_db_path = self.config.get('database.path', 'data/attendance.db')
                    shutil.copy2(file_path, current_db_path)

                    QMessageBox.information(
                        self,
                        "تم الاستعادة",
                        "تم استعادة النسخة الاحتياطية بنجاح"
                    )

                    self.refresh_statistics()

                except Exception as e:
                    logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
                    QMessageBox.critical(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية: {e}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.backup_thread and self.backup_thread.isRunning():
            self.backup_thread.quit()
            self.backup_thread.wait()

        event.accept()


def main():
    """تشغيل نافذة الإعدادات للاختبار"""
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = SettingsWindow()
    window.show()

    return app.exec()


if __name__ == "__main__":
    sys.exit(main())