"""
النافذة الرئيسية للتطبيق
Main application window
"""

import sys
from pathlib import Path
from datetime import datetime, date
from typing import Optional, List

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QComboBox,
    QDateEdit, QTextEdit, QGroupBox, QSplitter, QStatusBar,
    QMenuBar, QMenu, QToolBar, QMessageBox, QProgressBar,
    QCheckBox, QLineEdit, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, QDate, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap, QAction

# إضافة مسار src إلى sys.path للاستيراد
sys.path.append(str(Path(__file__).parent.parent))
from utils.config import Config
from utils.logger import get_logger
from data.database_manager import DatabaseManager
from services.absence_service import AbsenceService
from services.report_service import ReportService
from services.hijri_date_service import HijriDateService
from ui.reports_window import ReportsWindow
from ui.settings_window import SettingsWindow

logger = get_logger("MainWindow")


class MainWindow(QMainWindow):
    """
    النافذة الرئيسية للتطبيق
    Main application window
    """
    
    def __init__(self):
        super().__init__()
        
        # تهيئة الخدمات
        self.config = Config()
        self.db_manager = DatabaseManager(self.config)
        self.absence_service = AbsenceService(self.db_manager)
        self.report_service = ReportService(self.db_manager)
        self.hijri_service = HijriDateService()
        
        # متغيرات النافذة
        self.students_data = []
        self.current_date = date.today()
        
        # إعداد النافذة
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        logger.info("تم تهيئة النافذة الرئيسية")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام تسجيل غياب الطلاب - مدرسة أبو عبيدة المتوسطة")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد الخط العربي
        arabic_font = QFont("Arial", 12)
        arabic_font.setFamily("Tahoma")
        self.setFont(arabic_font)
        
        # إعداد اتجاه النص (من اليمين لليسار)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء القائمة الرئيسية"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة الملف
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        daily_report_action = QAction("تقرير يومي", self)
        daily_report_action.triggered.connect(self.show_reports_window)
        reports_menu.addAction(daily_report_action)

        weekly_report_action = QAction("تقرير أسبوعي", self)
        weekly_report_action.triggered.connect(self.show_reports_window)
        reports_menu.addAction(weekly_report_action)

        monthly_report_action = QAction("تقرير شهري", self)
        monthly_report_action.triggered.connect(self.show_reports_window)
        reports_menu.addAction(monthly_report_action)

        reports_menu.addSeparator()

        reports_window_action = QAction("نافذة التقارير", self)
        reports_window_action.triggered.connect(self.show_reports_window)
        reports_menu.addAction(reports_window_action)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")
        
        import_action = QAction("استيراد البيانات", self)
        tools_menu.addAction(import_action)
        
        export_action = QAction("تصدير البيانات", self)
        tools_menu.addAction(export_action)
        
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings_window)
        tools_menu.addAction(settings_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار شريط الأدوات
        save_btn = QPushButton("حفظ الغياب")
        save_btn.clicked.connect(self.save_absences)
        toolbar.addWidget(save_btn)
        
        toolbar.addSeparator()
        
        report_btn = QPushButton("تقرير سريع")
        report_btn.clicked.connect(self.generate_quick_report)
        toolbar.addWidget(report_btn)
        
        toolbar.addSeparator()
        
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar.addWidget(refresh_btn)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # قسم معلومات التاريخ
        date_group = self.create_date_section()
        main_layout.addWidget(date_group)
        
        # قسم تسجيل الغياب
        attendance_group = self.create_attendance_section()
        main_layout.addWidget(attendance_group)
        
        # قسم الإحصائيات السريعة
        stats_group = self.create_stats_section()
        main_layout.addWidget(stats_group)
    
    def create_date_section(self):
        """إنشاء قسم التاريخ"""
        group = QGroupBox("معلومات التاريخ")
        layout = QHBoxLayout(group)
        
        # التاريخ الميلادي
        layout.addWidget(QLabel("التاريخ الميلادي:"))
        self.gregorian_date = QDateEdit()
        self.gregorian_date.setDate(QDate.currentDate())
        self.gregorian_date.setCalendarPopup(True)
        self.gregorian_date.dateChanged.connect(self.on_date_changed)
        layout.addWidget(self.gregorian_date)
        
        # التاريخ الهجري
        layout.addWidget(QLabel("التاريخ الهجري:"))
        self.hijri_label = QLabel()
        self.update_hijri_date()
        layout.addWidget(self.hijri_label)
        
        layout.addStretch()
        
        return group
    
    def create_attendance_section(self):
        """إنشاء قسم تسجيل الغياب"""
        group = QGroupBox("تسجيل الغياب")
        layout = QVBoxLayout(group)
        
        # أدوات التحكم العلوية
        controls_layout = QHBoxLayout()
        
        # فلتر الصف
        controls_layout.addWidget(QLabel("الصف:"))
        self.grade_filter = QComboBox()
        self.grade_filter.addItem("جميع الصفوف")
        self.grade_filter.currentTextChanged.connect(self.filter_students)
        controls_layout.addWidget(self.grade_filter)
        
        # البحث
        controls_layout.addWidget(QLabel("البحث:"))
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("ابحث عن طالب...")
        self.search_box.textChanged.connect(self.filter_students)
        controls_layout.addWidget(self.search_box)
        
        controls_layout.addStretch()
        
        # أزرار سريعة
        mark_all_btn = QPushButton("تحديد الكل")
        mark_all_btn.clicked.connect(self.mark_all_absent)
        controls_layout.addWidget(mark_all_btn)
        
        clear_all_btn = QPushButton("إلغاء الكل")
        clear_all_btn.clicked.connect(self.clear_all_absent)
        controls_layout.addWidget(clear_all_btn)
        
        layout.addLayout(controls_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(6)
        self.students_table.setHorizontalHeaderLabels([
            "غائب", "اسم الطالب", "رقم الهوية", "الصف", "نوع الغياب", "السبب"
        ])
        
        # تخصيص عرض الأعمدة
        header = self.students_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.students_table)
        
        return group
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات"""
        group = QGroupBox("إحصائيات سريعة")
        layout = QHBoxLayout(group)
        
        # إجمالي الطلاب
        self.total_students_label = QLabel("إجمالي الطلاب: 0")
        layout.addWidget(self.total_students_label)
        
        # الطلاب الحاضرون
        self.present_students_label = QLabel("الحاضرون: 0")
        layout.addWidget(self.present_students_label)
        
        # الطلاب الغائبون
        self.absent_students_label = QLabel("الغائبون: 0")
        layout.addWidget(self.absent_students_label)
        
        # نسبة الغياب
        self.absence_rate_label = QLabel("نسبة الغياب: 0%")
        layout.addWidget(self.absence_rate_label)
        
        layout.addStretch()
        
        return group
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # رسالة الحالة
        self.status_bar.showMessage("جاهز")
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def setup_connections(self):
        """إعداد الاتصالات بين العناصر"""
        pass
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل قائمة الطلاب
            self.load_students()
            
            # تحميل قائمة الصفوف
            self.load_grades()
            
            # تحديث الإحصائيات
            self.update_statistics()
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {e}")
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            # الحصول على قائمة الطلاب من قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, civil_id, grade_code, phone, parent_phone, address
                FROM students 
                WHERE is_active = 1
                ORDER BY grade_code, name
            """)
            
            self.students_data = cursor.fetchall()
            conn.close()
            
            # تحديث الجدول
            self.update_students_table()
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الطلاب: {e}")
            raise
    
    def load_grades(self):
        """تحميل قائمة الصفوف"""
        try:
            # استخراج الصفوف من بيانات الطلاب
            grades = set()
            for student in self.students_data:
                grades.add(student[3])  # grade_code
            
            # تحديث قائمة الصفوف
            self.grade_filter.clear()
            self.grade_filter.addItem("جميع الصفوف")
            for grade in sorted(grades):
                self.grade_filter.addItem(grade)
                
        except Exception as e:
            logger.error(f"خطأ في تحميل الصفوف: {e}")
    
    def update_students_table(self):
        """تحديث جدول الطلاب"""
        try:
            # تطبيق الفلاتر
            filtered_students = self.get_filtered_students()

            # تحديث عدد الصفوف
            self.students_table.setRowCount(len(filtered_students))

            for row, student in enumerate(filtered_students):
                student_id, name, civil_id, grade_code, phone, parent_phone, address = student

                # عمود الغياب (checkbox)
                absent_checkbox = QCheckBox()
                absent_checkbox.setProperty("student_id", student_id)
                absent_checkbox.stateChanged.connect(self.on_absence_changed)
                self.students_table.setCellWidget(row, 0, absent_checkbox)

                # اسم الطالب
                self.students_table.setItem(row, 1, QTableWidgetItem(name))

                # رقم الهوية
                self.students_table.setItem(row, 2, QTableWidgetItem(civil_id))

                # الصف
                self.students_table.setItem(row, 3, QTableWidgetItem(grade_code))

                # نوع الغياب
                absence_type_combo = QComboBox()
                absence_type_combo.addItems(["غياب", "غياب بعذر", "تأخير", "انصراف مبكر"])
                absence_type_combo.setProperty("student_id", student_id)
                self.students_table.setCellWidget(row, 4, absence_type_combo)

                # السبب
                reason_edit = QLineEdit()
                reason_edit.setPlaceholderText("اختياري...")
                reason_edit.setProperty("student_id", student_id)
                self.students_table.setCellWidget(row, 5, reason_edit)

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            logger.error(f"خطأ في تحديث جدول الطلاب: {e}")

    def get_filtered_students(self):
        """الحصول على الطلاب المفلترين"""
        filtered = self.students_data

        # فلتر الصف
        selected_grade = self.grade_filter.currentText()
        if selected_grade != "جميع الصفوف":
            filtered = [s for s in filtered if s[3] == selected_grade]

        # فلتر البحث
        search_text = self.search_box.text().strip().lower()
        if search_text:
            filtered = [s for s in filtered if search_text in s[1].lower() or search_text in s[2]]

        return filtered
    
    def update_hijri_date(self):
        """تحديث التاريخ الهجري"""
        try:
            hijri_date = self.hijri_service.gregorian_to_hijri(self.current_date)
            self.hijri_label.setText(hijri_date)
        except Exception as e:
            logger.error(f"خطأ في تحديث التاريخ الهجري: {e}")
            self.hijri_label.setText("غير متاح")
    
    def on_date_changed(self, qdate):
        """عند تغيير التاريخ"""
        self.current_date = qdate.toPython()
        self.update_hijri_date()
        # تحديث بيانات الغياب للتاريخ الجديد
        self.load_absences_for_date()
    
    def load_absences_for_date(self):
        """تحميل بيانات الغياب للتاريخ المحدد"""
        try:
            # الحصول على بيانات الغياب للتاريخ المحدد
            absences = self.absence_service.get_absences_by_date(self.current_date)

            # تحديث حالة الغياب في الجدول
            for row in range(self.students_table.rowCount()):
                checkbox = self.students_table.cellWidget(row, 0)
                absence_type_combo = self.students_table.cellWidget(row, 4)
                reason_edit = self.students_table.cellWidget(row, 5)

                if checkbox:
                    student_id = checkbox.property("student_id")

                    # البحث عن غياب هذا الطالب
                    student_absence = next((a for a in absences if a.get('student_id') == student_id), None)

                    if student_absence:
                        checkbox.setChecked(True)
                        if absence_type_combo:
                            absence_type_combo.setCurrentText(student_absence.get('absence_type', 'غياب'))
                        if reason_edit:
                            reason_edit.setText(student_absence.get('reason', ''))
                    else:
                        checkbox.setChecked(False)
                        if absence_type_combo:
                            absence_type_combo.setCurrentText('غياب')
                        if reason_edit:
                            reason_edit.setText('')

            self.update_statistics()

        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الغياب: {e}")

    def filter_students(self):
        """فلترة الطلاب حسب الصف والبحث"""
        self.update_students_table()

    def mark_all_absent(self):
        """تحديد جميع الطلاب كغائبين"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.update_statistics()

    def clear_all_absent(self):
        """إلغاء تحديد جميع الطلاب"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.update_statistics()

    def on_absence_changed(self):
        """عند تغيير حالة الغياب"""
        self.update_statistics()
    
    def save_absences(self):
        """حفظ بيانات الغياب"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
            self.status_bar.showMessage("جاري حفظ بيانات الغياب...")

            absences_to_save = []

            # جمع بيانات الغياب من الجدول
            for row in range(self.students_table.rowCount()):
                checkbox = self.students_table.cellWidget(row, 0)
                absence_type_combo = self.students_table.cellWidget(row, 4)
                reason_edit = self.students_table.cellWidget(row, 5)

                if checkbox and checkbox.isChecked():
                    student_id = checkbox.property("student_id")
                    absence_type = absence_type_combo.currentText() if absence_type_combo else "غياب"
                    reason = reason_edit.text().strip() if reason_edit else ""

                    absences_to_save.append({
                        'student_id': student_id,
                        'absence_date': self.current_date,
                        'absence_type': absence_type,
                        'reason': reason,
                        'is_excused': absence_type == "غياب بعذر"
                    })

            # حفظ البيانات
            if absences_to_save:
                result = self.absence_service.record_bulk_absences(absences_to_save)

                if result['success']:
                    QMessageBox.information(
                        self,
                        "نجح الحفظ",
                        f"تم حفظ {result['saved_count']} غياب بنجاح"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        f"تم حفظ {result['saved_count']} من {len(absences_to_save)} غياب\n"
                        f"فشل في حفظ {result['failed_count']} غياب"
                    )
            else:
                # حذف جميع الغيابات للتاريخ المحدد إذا لم يتم تحديد أي طالب
                self.absence_service.delete_absences_by_date(self.current_date)
                QMessageBox.information(self, "تم الحفظ", "تم حفظ البيانات (لا يوجد غيابات)")

            self.progress_bar.setVisible(False)
            self.status_bar.showMessage("تم حفظ البيانات بنجاح", 3000)

        except Exception as e:
            self.progress_bar.setVisible(False)
            logger.error(f"خطأ في حفظ بيانات الغياب: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات: {e}")

    def generate_quick_report(self):
        """إنشاء تقرير سريع"""
        try:
            # إنشاء تقرير يومي للتاريخ الحالي
            report_data = self.absence_service.get_daily_statistics(self.current_date)

            if not report_data:
                QMessageBox.information(self, "تقرير", "لا توجد بيانات غياب لهذا التاريخ")
                return

            # إنشاء ملف Word
            output_path = f"تقرير_غياب_{self.current_date.strftime('%Y-%m-%d')}.docx"

            success = self.report_service.generate_daily_report(
                date=self.current_date,
                output_path=output_path
            )

            if success:
                QMessageBox.information(
                    self,
                    "تم إنشاء التقرير",
                    f"تم إنشاء التقرير بنجاح:\n{output_path}"
                )
            else:
                QMessageBox.warning(self, "تحذير", "فشل في إنشاء التقرير")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_initial_data()
        self.status_bar.showMessage("تم تحديث البيانات", 2000)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات الطلاب المعروضين حالياً
            displayed_students = self.students_table.rowCount()
            self.total_students_label.setText(f"إجمالي الطلاب: {displayed_students}")

            # عد الطلاب الغائبين
            absent_count = 0
            for row in range(displayed_students):
                checkbox = self.students_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    absent_count += 1

            present_count = displayed_students - absent_count

            # تحديث التسميات
            self.present_students_label.setText(f"الحاضرون: {present_count}")
            self.absent_students_label.setText(f"الغائبون: {absent_count}")

            # حساب نسبة الغياب
            if displayed_students > 0:
                absence_rate = (absent_count / displayed_students) * 100
                self.absence_rate_label.setText(f"نسبة الغياب: {absence_rate:.1f}%")
            else:
                self.absence_rate_label.setText("نسبة الغياب: 0%")

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")

    def show_reports_window(self):
        """عرض نافذة التقارير"""
        try:
            if not hasattr(self, 'reports_window') or self.reports_window is None:
                self.reports_window = ReportsWindow(self)

            self.reports_window.show()
            self.reports_window.raise_()
            self.reports_window.activateWindow()

        except Exception as e:
            logger.error(f"خطأ في عرض نافذة التقارير: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير: {e}")

    def show_settings_window(self):
        """عرض نافذة الإعدادات"""
        try:
            if not hasattr(self, 'settings_window') or self.settings_window is None:
                self.settings_window = SettingsWindow(self)

            self.settings_window.show()
            self.settings_window.raise_()
            self.settings_window.activateWindow()

        except Exception as e:
            logger.error(f"خطأ في عرض نافذة الإعدادات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {e}")


def main():
    """تشغيل التطبيق"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي للتطبيق
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = MainWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
