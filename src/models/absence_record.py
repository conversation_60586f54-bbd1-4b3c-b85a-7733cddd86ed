"""
نموذج سجل الغياب
Absence record model
"""

from datetime import datetime, date
from typing import Any, Dict, Optional

from .base_model import BaseModel
from ..utils.constants import ABSENCE_TYPES, PERIODS, DATA_LIMITS
from ..utils.helpers import format_date_arabic, get_weekday_arabic
from ..utils.exceptions import InvalidDateException


class AbsenceRecord(BaseModel):
    """
    نموذج سجل الغياب
    Absence record data model
    """
    
    def __init__(self):
        super().__init__()
        self.student_id: Optional[str] = None
        self.absence_date: Optional[date] = None
        self.absence_type: Optional[str] = None  # "EXCUSED" or "UNEXCUSED"
        self.reason: Optional[str] = None
        self.period: Optional[str] = None  # "MORNING" or "EVENING"
        self.semester: Optional[str] = None
        self.academic_year: Optional[str] = None
        self.recorded_by: Optional[str] = None
        self.notes: Optional[str] = None
        
        # بيانات إضافية للتقارير
        self.student_name: Optional[str] = None
        self.student_civil_id: Optional[str] = None
        self.grade_code: Optional[str] = None
    
    def validate(self) -> bool:
        """
        التحقق من صحة بيانات سجل الغياب
        Validate absence record data
        """
        self.clear_errors()
        is_valid = True
        
        # التحقق من الحقول المطلوبة
        if not self.validate_required_field("student_id", self.student_id, "معرف الطالب"):
            is_valid = False
        
        if not self.validate_required_field("absence_date", self.absence_date, "تاريخ الغياب"):
            is_valid = False
        
        if not self.validate_required_field("absence_type", self.absence_type, "نوع الغياب"):
            is_valid = False
        
        # التحقق من تاريخ الغياب
        if self.absence_date:
            if not isinstance(self.absence_date, date):
                self.add_error("تاريخ الغياب غير صحيح")
                is_valid = False
            elif self.absence_date > date.today():
                self.add_error("لا يمكن تسجيل غياب في المستقبل")
                is_valid = False
        
        # التحقق من نوع الغياب
        if self.absence_type:
            if not self.validate_choice("absence_type", self.absence_type, 
                                      list(ABSENCE_TYPES.keys()), "نوع الغياب"):
                is_valid = False
        
        # التحقق من الفترة
        if self.period:
            if not self.validate_choice("period", self.period, 
                                      list(PERIODS.keys()), "الفترة"):
                is_valid = False
        
        # التحقق من السبب (مطلوب للغياب بعذر)
        if self.absence_type == "EXCUSED":
            if not self.validate_required_field("reason", self.reason, "سبب الغياب"):
                is_valid = False
        
        # التحقق من طول السبب
        if self.reason:
            if not self.validate_string_length("reason", self.reason, 0, 
                                             DATA_LIMITS["MAX_ABSENCE_REASON_LENGTH"], "سبب الغياب"):
                is_valid = False
        
        # التحقق من الملاحظات
        if self.notes:
            if not self.validate_string_length("notes", self.notes, 0, 1000, "الملاحظات"):
                is_valid = False
        
        return is_valid
    
    def to_dict(self) -> Dict[str, Any]:
        """
        تحويل سجل الغياب إلى قاموس
        Convert absence record to dictionary
        """
        return {
            "id": self.id,
            "student_id": self.student_id,
            "absence_date": self.absence_date.isoformat() if self.absence_date else None,
            "absence_type": self.absence_type,
            "reason": self.reason,
            "period": self.period,
            "semester": self.semester,
            "academic_year": self.academic_year,
            "recorded_by": self.recorded_by,
            "notes": self.notes,
            "student_name": self.student_name,
            "student_civil_id": self.student_civil_id,
            "grade_code": self.grade_code,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def from_dict(self, data: Dict[str, Any]) -> 'AbsenceRecord':
        """
        إنشاء سجل الغياب من قاموس
        Create absence record from dictionary
        """
        self.id = data.get("id")
        self.student_id = data.get("student_id")
        self.absence_type = data.get("absence_type")
        self.reason = data.get("reason")
        self.period = data.get("period")
        self.semester = data.get("semester")
        self.academic_year = data.get("academic_year")
        self.recorded_by = data.get("recorded_by")
        self.notes = data.get("notes")
        self.student_name = data.get("student_name")
        self.student_civil_id = data.get("student_civil_id")
        self.grade_code = data.get("grade_code")
        
        # تحويل التواريخ
        if data.get("absence_date"):
            if isinstance(data["absence_date"], str):
                self.absence_date = datetime.fromisoformat(data["absence_date"]).date()
            else:
                self.absence_date = data["absence_date"]
        
        if data.get("created_at"):
            self.created_at = datetime.fromisoformat(data["created_at"].replace('Z', '+00:00'))
        if data.get("updated_at"):
            self.updated_at = datetime.fromisoformat(data["updated_at"].replace('Z', '+00:00'))
        
        return self
    
    def get_absence_type_display(self) -> str:
        """
        الحصول على نوع الغياب للعرض
        Get absence type for display
        """
        return ABSENCE_TYPES.get(self.absence_type, "غير محدد")
    
    def get_period_display(self) -> str:
        """
        الحصول على الفترة للعرض
        Get period for display
        """
        return PERIODS.get(self.period, "غير محدد")
    
    def get_formatted_date(self) -> str:
        """
        الحصول على التاريخ منسق
        Get formatted date
        """
        if not self.absence_date:
            return ""
        return format_date_arabic(self.absence_date)
    
    def get_weekday(self) -> str:
        """
        الحصول على يوم الأسبوع
        Get weekday
        """
        if not self.absence_date:
            return ""
        return get_weekday_arabic(self.absence_date)
    
    def get_full_date_info(self) -> str:
        """
        الحصول على معلومات التاريخ الكاملة
        Get full date information
        """
        if not self.absence_date:
            return ""
        
        formatted_date = self.get_formatted_date()
        weekday = self.get_weekday()
        return f"{weekday} {formatted_date}"
    
    def is_excused(self) -> bool:
        """
        التحقق من كون الغياب بعذر
        Check if absence is excused
        """
        return self.absence_type == "EXCUSED"
    
    def is_unexcused(self) -> bool:
        """
        التحقق من كون الغياب بدون عذر
        Check if absence is unexcused
        """
        return self.absence_type == "UNEXCUSED"
    
    def get_display_reason(self) -> str:
        """
        الحصول على السبب للعرض
        Get reason for display
        """
        if self.is_excused() and self.reason:
            return self.reason
        elif self.is_unexcused():
            return "غياب بدون عذر"
        else:
            return "غير محدد"
    
    def get_summary(self) -> str:
        """
        الحصول على ملخص سجل الغياب
        Get absence record summary
        """
        parts = []
        
        if self.student_name:
            parts.append(f"الطالب: {self.student_name}")
        
        if self.absence_date:
            parts.append(f"التاريخ: {self.get_full_date_info()}")
        
        parts.append(f"النوع: {self.get_absence_type_display()}")
        
        if self.period:
            parts.append(f"الفترة: {self.get_period_display()}")
        
        if self.reason:
            parts.append(f"السبب: {self.reason}")
        
        return " | ".join(parts)
    
    def is_in_date_range(self, start_date: date, end_date: date) -> bool:
        """
        التحقق من كون الغياب في نطاق تاريخ معين
        Check if absence is in date range
        """
        if not self.absence_date:
            return False
        return start_date <= self.absence_date <= end_date
    
    def is_in_month(self, year: int, month: int) -> bool:
        """
        التحقق من كون الغياب في شهر معين
        Check if absence is in specific month
        """
        if not self.absence_date:
            return False
        return self.absence_date.year == year and self.absence_date.month == month
    
    def is_in_semester(self, semester: str, academic_year: str) -> bool:
        """
        التحقق من كون الغياب في فصل دراسي معين
        Check if absence is in specific semester
        """
        return self.semester == semester and self.academic_year == academic_year
    
    def update_reason(self, new_reason: str):
        """
        تحديث سبب الغياب
        Update absence reason
        """
        self.reason = new_reason.strip() if new_reason else None
        self.updated_at = datetime.now()
    
    def add_note(self, note: str):
        """
        إضافة ملاحظة
        Add note
        """
        if not note or not note.strip():
            return
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        new_note = f"[{current_time}] {note.strip()}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
        
        self.updated_at = datetime.now()
    
    def __str__(self) -> str:
        """
        تمثيل نصي لسجل الغياب
        String representation
        """
        return f"غياب: {self.student_name} - {self.get_formatted_date()}"
    
    def __repr__(self) -> str:
        """
        تمثيل تفصيلي لسجل الغياب
        Detailed representation
        """
        return f"AbsenceRecord(id={self.id}, student_id={self.student_id}, date={self.absence_date})"
