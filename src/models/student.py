"""
نموذج الطالب
Student model
"""

from datetime import datetime
from typing import Any, Dict, Optional

from .base_model import BaseModel
from ..utils.constants import GRADE_CODES, DATA_LIMITS
from ..utils.helpers import validate_civil_id, validate_phone, format_phone, validate_grade_code
from ..utils.exceptions import InvalidStudentDataException, InvalidCivilIdException, InvalidPhoneException


class Student(BaseModel):
    """
    نموذج بيانات الطالب
    Student data model
    """
    
    def __init__(self):
        super().__init__()
        self.civil_id: Optional[str] = None
        self.name: Optional[str] = None
        self.phone: Optional[str] = None
        self.grade_code: Optional[str] = None
        self.semester: Optional[str] = None
        self.is_active: bool = True
        self.notes: Optional[str] = None
    
    def validate(self) -> bool:
        """
        التحقق من صحة بيانات الطالب
        Validate student data
        """
        self.clear_errors()
        is_valid = True
        
        # التحقق من الحقول المطلوبة
        if not self.validate_required_field("civil_id", self.civil_id, "رقم الهوية المدنية"):
            is_valid = False
        
        if not self.validate_required_field("name", self.name, "اسم الطالب"):
            is_valid = False
        
        if not self.validate_required_field("grade_code", self.grade_code, "رمز الصف"):
            is_valid = False
        
        # التحقق من رقم الهوية المدنية
        if self.civil_id:
            if not validate_civil_id(self.civil_id):
                self.add_error("رقم الهوية المدنية غير صحيح")
                is_valid = False
        
        # التحقق من اسم الطالب
        if self.name:
            if not self.validate_string_length("name", self.name, 2, DATA_LIMITS["MAX_STUDENT_NAME_LENGTH"], "اسم الطالب"):
                is_valid = False
        
        # التحقق من رقم الجوال
        if self.phone:
            if not validate_phone(self.phone):
                self.add_error("رقم الجوال غير صحيح")
                is_valid = False
        
        # التحقق من رمز الصف
        if self.grade_code:
            if not validate_grade_code(self.grade_code):
                self.add_error("رمز الصف غير صحيح")
                is_valid = False
        
        # التحقق من الملاحظات
        if self.notes:
            if not self.validate_string_length("notes", self.notes, 0, 1000, "الملاحظات"):
                is_valid = False
        
        return is_valid
    
    def to_dict(self) -> Dict[str, Any]:
        """
        تحويل نموذج الطالب إلى قاموس
        Convert student model to dictionary
        """
        return {
            "id": self.id,
            "civil_id": self.civil_id,
            "name": self.name,
            "phone": self.phone,
            "grade_code": self.grade_code,
            "semester": self.semester,
            "is_active": self.is_active,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def from_dict(self, data: Dict[str, Any]) -> 'Student':
        """
        إنشاء نموذج الطالب من قاموس
        Create student model from dictionary
        """
        self.id = data.get("id")
        self.civil_id = data.get("civil_id")
        self.name = data.get("name")
        self.phone = data.get("phone")
        self.grade_code = data.get("grade_code")
        self.semester = data.get("semester")
        self.is_active = data.get("is_active", True)
        self.notes = data.get("notes")
        
        # تحويل التواريخ
        if data.get("created_at"):
            self.created_at = datetime.fromisoformat(data["created_at"].replace('Z', '+00:00'))
        if data.get("updated_at"):
            self.updated_at = datetime.fromisoformat(data["updated_at"].replace('Z', '+00:00'))
        
        return self
    
    def get_grade_name(self) -> str:
        """
        الحصول على اسم الصف
        Get grade name
        """
        return GRADE_CODES.get(self.grade_code, "غير محدد")
    
    def get_formatted_phone(self) -> str:
        """
        الحصول على رقم الجوال منسق
        Get formatted phone number
        """
        if not self.phone:
            return ""
        return format_phone(self.phone)
    
    def get_display_name(self) -> str:
        """
        الحصول على الاسم للعرض
        Get display name
        """
        if not self.name:
            return "غير محدد"
        return self.name.strip()
    
    def get_full_info(self) -> str:
        """
        الحصول على المعلومات الكاملة للطالب
        Get full student information
        """
        info_parts = []
        
        if self.name:
            info_parts.append(f"الاسم: {self.name}")
        
        if self.civil_id:
            info_parts.append(f"الهوية: {self.civil_id}")
        
        if self.grade_code:
            info_parts.append(f"الصف: {self.get_grade_name()}")
        
        if self.phone:
            info_parts.append(f"الجوال: {self.get_formatted_phone()}")
        
        return " | ".join(info_parts)
    
    def is_in_grade(self, grade_code: str) -> bool:
        """
        التحقق من كون الطالب في صف معين
        Check if student is in specific grade
        """
        return self.grade_code == grade_code
    
    def activate(self):
        """
        تفعيل الطالب
        Activate student
        """
        self.is_active = True
        self.updated_at = datetime.now()
    
    def deactivate(self):
        """
        إلغاء تفعيل الطالب
        Deactivate student
        """
        self.is_active = False
        self.updated_at = datetime.now()
    
    def update_phone(self, new_phone: str) -> bool:
        """
        تحديث رقم الجوال
        Update phone number
        """
        if validate_phone(new_phone):
            self.phone = format_phone(new_phone)
            self.updated_at = datetime.now()
            return True
        return False
    
    def update_grade(self, new_grade_code: str) -> bool:
        """
        تحديث الصف
        Update grade
        """
        if validate_grade_code(new_grade_code):
            self.grade_code = new_grade_code
            self.updated_at = datetime.now()
            return True
        return False
    
    def add_note(self, note: str):
        """
        إضافة ملاحظة
        Add note
        """
        if not note or not note.strip():
            return
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        new_note = f"[{current_time}] {note.strip()}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
        
        self.updated_at = datetime.now()
    
    def __str__(self) -> str:
        """
        تمثيل نصي للطالب
        String representation
        """
        return f"الطالب: {self.name} ({self.civil_id})"
    
    def __repr__(self) -> str:
        """
        تمثيل تفصيلي للطالب
        Detailed representation
        """
        return f"Student(id={self.id}, name={self.name}, civil_id={self.civil_id}, grade={self.grade_code})"
    
    @classmethod
    def create_from_excel_row(cls, row_data: Dict[str, Any]) -> 'Student':
        """
        إنشاء طالب من بيانات صف Excel
        Create student from Excel row data
        """
        student = cls()
        
        # تعيين البيانات من Excel
        student.civil_id = str(row_data.get("Civil ID", "")).strip()
        student.name = str(row_data.get("Student Name", "")).strip()
        student.phone = str(row_data.get("Phone", "")).strip()
        student.grade_code = str(row_data.get("Grade Code", "")).strip()
        student.semester = str(row_data.get("Semester", "")).strip()
        
        # إنشاء معرف فريد
        student.id = student.generate_id()
        student.set_timestamps(is_new=True)
        
        return student
