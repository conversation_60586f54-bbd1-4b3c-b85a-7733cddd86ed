"""
النموذج الأساسي
Base model for all data models
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional
import uuid

from ..utils.logger import get_logger
from ..utils.exceptions import ValidationException

logger = get_logger("BaseModel")


class BaseModel(ABC):
    """
    النموذج الأساسي لجميع نماذج البيانات
    Base model for all data models
    """
    
    def __init__(self):
        self.id: Optional[str] = None
        self.created_at: Optional[datetime] = None
        self.updated_at: Optional[datetime] = None
        self._errors: List[str] = []
    
    def generate_id(self) -> str:
        """
        إنشاء معرف فريد
        Generate unique ID
        """
        return str(uuid.uuid4())
    
    def set_timestamps(self, is_new: bool = True):
        """
        تعيين الطوابع الزمنية
        Set timestamps
        """
        now = datetime.now()
        if is_new:
            self.created_at = now
        self.updated_at = now
    
    @abstractmethod
    def validate(self) -> bool:
        """
        التحقق من صحة البيانات
        Validate model data
        """
        pass
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """
        تحويل النموذج إلى قاموس
        Convert model to dictionary
        """
        pass
    
    @abstractmethod
    def from_dict(self, data: Dict[str, Any]) -> 'BaseModel':
        """
        إنشاء النموذج من قاموس
        Create model from dictionary
        """
        pass
    
    def add_error(self, error: str):
        """
        إضافة خطأ للقائمة
        Add error to list
        """
        self._errors.append(error)
        logger.warning(f"خطأ في التحقق: {error}")
    
    def clear_errors(self):
        """
        مسح قائمة الأخطاء
        Clear errors list
        """
        self._errors.clear()
    
    def get_errors(self) -> List[str]:
        """
        الحصول على قائمة الأخطاء
        Get errors list
        """
        return self._errors.copy()
    
    def has_errors(self) -> bool:
        """
        التحقق من وجود أخطاء
        Check if has errors
        """
        return len(self._errors) > 0
    
    def get_error_message(self) -> str:
        """
        الحصول على رسالة الأخطاء مجمعة
        Get combined error message
        """
        if not self._errors:
            return ""
        return " | ".join(self._errors)
    
    def validate_required_field(self, field_name: str, value: Any, field_display_name: str = None) -> bool:
        """
        التحقق من الحقول المطلوبة
        Validate required fields
        """
        display_name = field_display_name or field_name
        if value is None or (isinstance(value, str) and not value.strip()):
            self.add_error(f"الحقل '{display_name}' مطلوب")
            return False
        return True
    
    def validate_string_length(self, field_name: str, value: str, min_length: int = 0, 
                             max_length: int = None, field_display_name: str = None) -> bool:
        """
        التحقق من طول النص
        Validate string length
        """
        display_name = field_display_name or field_name
        if not isinstance(value, str):
            return True
        
        length = len(value.strip())
        
        if length < min_length:
            self.add_error(f"الحقل '{display_name}' يجب أن يكون على الأقل {min_length} أحرف")
            return False
        
        if max_length and length > max_length:
            self.add_error(f"الحقل '{display_name}' يجب أن يكون أقل من {max_length} حرف")
            return False
        
        return True
    
    def validate_numeric_range(self, field_name: str, value: Any, min_value: float = None, 
                             max_value: float = None, field_display_name: str = None) -> bool:
        """
        التحقق من النطاق الرقمي
        Validate numeric range
        """
        display_name = field_display_name or field_name
        
        try:
            num_value = float(value)
        except (ValueError, TypeError):
            self.add_error(f"الحقل '{display_name}' يجب أن يكون رقماً")
            return False
        
        if min_value is not None and num_value < min_value:
            self.add_error(f"الحقل '{display_name}' يجب أن يكون أكبر من أو يساوي {min_value}")
            return False
        
        if max_value is not None and num_value > max_value:
            self.add_error(f"الحقل '{display_name}' يجب أن يكون أقل من أو يساوي {max_value}")
            return False
        
        return True
    
    def validate_date(self, field_name: str, value: Any, field_display_name: str = None) -> bool:
        """
        التحقق من صحة التاريخ
        Validate date
        """
        display_name = field_display_name or field_name
        
        if value is None:
            return True
        
        if not isinstance(value, (datetime, str)):
            self.add_error(f"الحقل '{display_name}' يجب أن يكون تاريخاً صحيحاً")
            return False
        
        if isinstance(value, str):
            try:
                datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError:
                self.add_error(f"الحقل '{display_name}' يجب أن يكون تاريخاً صحيحاً")
                return False
        
        return True
    
    def validate_choice(self, field_name: str, value: Any, choices: List[Any], 
                       field_display_name: str = None) -> bool:
        """
        التحقق من الخيارات المحددة
        Validate choices
        """
        display_name = field_display_name or field_name
        
        if value not in choices:
            choices_str = ", ".join(str(choice) for choice in choices)
            self.add_error(f"الحقل '{display_name}' يجب أن يكون أحد القيم التالية: {choices_str}")
            return False
        
        return True
    
    def __str__(self) -> str:
        """
        تمثيل نصي للنموذج
        String representation
        """
        return f"{self.__class__.__name__}(id={self.id})"
    
    def __repr__(self) -> str:
        """
        تمثيل تفصيلي للنموذج
        Detailed representation
        """
        return f"{self.__class__.__name__}(id={self.id}, created_at={self.created_at})"
    
    def __eq__(self, other) -> bool:
        """
        مقارنة النماذج
        Compare models
        """
        if not isinstance(other, self.__class__):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """
        حساب hash للنموذج
        Calculate model hash
        """
        return hash(self.id) if self.id else hash(id(self))
