"""
مدير قاعدة البيانات
Database manager for SQLite operations
"""

import sqlite3
import os
import shutil
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Tuple
from contextlib import contextmanager

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import (
    DatabaseException, DatabaseConnectionException, 
    DatabaseQueryException, DatabaseIntegrityException
)
from ..utils.helpers import create_directory

logger = get_logger("DatabaseManager")


class DatabaseManager:
    """
    مدير قاعدة البيانات SQLite
    SQLite database manager
    """
    
    def __init__(self, db_path: str = None):
        self.config = get_config()
        self.db_path = db_path or self.config.get("database.path", "data/attendance.db")
        self.backup_path = self.config.get("database.backup_path", "data/backups")
        self.timeout = self.config.get("database.timeout", 30)
        self.check_same_thread = self.config.get("database.check_same_thread", False)
        
        # إنشاء مجلدات قاعدة البيانات والنسخ الاحتياطية
        self._ensure_directories()
        
        # إنشاء قاعدة البيانات والجداول
        self._initialize_database()
    
    def _ensure_directories(self):
        """
        التأكد من وجود المجلدات المطلوبة
        Ensure required directories exist
        """
        db_dir = os.path.dirname(self.db_path)
        if db_dir:
            create_directory(db_dir)
        
        create_directory(self.backup_path)
    
    def _initialize_database(self):
        """
        تهيئة قاعدة البيانات وإنشاء الجداول
        Initialize database and create tables
        """
        try:
            with self.get_connection() as conn:
                self._create_tables(conn)
                self._create_indexes(conn)
                logger.info("تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise DatabaseException(f"فشل في تهيئة قاعدة البيانات: {e}")
    
    def _create_tables(self, conn: sqlite3.Connection):
        """
        إنشاء جداول قاعدة البيانات
        Create database tables
        """
        cursor = conn.cursor()
        
        # جدول الطلاب
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS students (
                id TEXT PRIMARY KEY,
                civil_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                phone TEXT,
                grade_code TEXT NOT NULL,
                semester TEXT,
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول سجلات الغياب
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS absence_records (
                id TEXT PRIMARY KEY,
                student_id TEXT NOT NULL,
                absence_date DATE NOT NULL,
                absence_type TEXT NOT NULL CHECK (absence_type IN ('EXCUSED', 'UNEXCUSED')),
                reason TEXT,
                period TEXT CHECK (period IN ('MORNING', 'EVENING')),
                semester TEXT,
                academic_year TEXT,
                recorded_by TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
                UNIQUE(student_id, absence_date, period)
            )
        """)
        
        # جدول معلومات المدرسة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS school_info (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                stage TEXT NOT NULL,
                stage_code TEXT,
                academic_year TEXT NOT NULL,
                current_semester TEXT NOT NULL,
                logo_path TEXT,
                address TEXT,
                phone TEXT,
                email TEXT,
                principal_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الصفوف
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS grades (
                id TEXT PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                stage TEXT NOT NULL,
                order_index INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول قوالب التقارير
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS report_templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                template_data TEXT NOT NULL,
                is_default BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول الإعدادات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT,
                category TEXT DEFAULT 'general',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # جدول سجل العمليات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS operation_log (
                id TEXT PRIMARY KEY,
                operation_type TEXT NOT NULL,
                table_name TEXT,
                record_id TEXT,
                old_data TEXT,
                new_data TEXT,
                user_name TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT
            )
        """)
        
        # جدول النسخ الاحتياطية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backups (
                id TEXT PRIMARY KEY,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                is_automatic BOOLEAN DEFAULT 1
            )
        """)
        
        # جدول الإحصائيات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS statistics (
                id TEXT PRIMARY KEY,
                stat_type TEXT NOT NULL,
                stat_date DATE NOT NULL,
                grade_code TEXT,
                semester TEXT,
                academic_year TEXT,
                total_students INTEGER DEFAULT 0,
                total_absences INTEGER DEFAULT 0,
                excused_absences INTEGER DEFAULT 0,
                unexcused_absences INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stat_type, stat_date, grade_code, semester, academic_year)
            )
        """)
        
        conn.commit()
        logger.info("تم إنشاء جداول قاعدة البيانات")
    
    def _create_indexes(self, conn: sqlite3.Connection):
        """
        إنشاء فهارس قاعدة البيانات
        Create database indexes
        """
        cursor = conn.cursor()
        
        # فهارس جدول الطلاب
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_students_civil_id ON students(civil_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_students_grade_code ON students(grade_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_students_active ON students(is_active)")
        
        # فهارس جدول سجلات الغياب
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_student_id ON absence_records(student_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_date ON absence_records(absence_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_type ON absence_records(absence_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_semester ON absence_records(semester, academic_year)")
        
        # فهارس جدول الإحصائيات
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(stat_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_statistics_grade ON statistics(grade_code)")
        
        # فهارس جدول سجل العمليات
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_operation_log_timestamp ON operation_log(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_operation_log_type ON operation_log(operation_type)")
        
        conn.commit()
        logger.info("تم إنشاء فهارس قاعدة البيانات")
    
    @contextmanager
    def get_connection(self):
        """
        الحصول على اتصال بقاعدة البيانات
        Get database connection
        """
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.timeout,
                check_same_thread=self.check_same_thread
            )
            conn.row_factory = sqlite3.Row  # للحصول على النتائج كقواميس
            conn.execute("PRAGMA foreign_keys = ON")  # تفعيل المفاتيح الخارجية
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            logger.error(f"خطأ في قاعدة البيانات: {e}")
            raise DatabaseConnectionException(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: Tuple = ()) -> List[sqlite3.Row]:
        """
        تنفيذ استعلام SELECT
        Execute SELECT query
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                results = cursor.fetchall()
                logger.debug(f"تم تنفيذ الاستعلام: {query[:100]}...")
                return results
        except sqlite3.Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise DatabaseQueryException(f"خطأ في تنفيذ الاستعلام: {e}")
    
    def execute_non_query(self, query: str, params: Tuple = ()) -> int:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE
        Execute INSERT/UPDATE/DELETE query
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                affected_rows = cursor.rowcount
                logger.debug(f"تم تنفيذ الاستعلام وتأثر {affected_rows} صف")
                return affected_rows
        except sqlite3.IntegrityError as e:
            logger.error(f"خطأ في تكامل البيانات: {e}")
            raise DatabaseIntegrityException(f"خطأ في تكامل البيانات: {e}")
        except sqlite3.Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise DatabaseQueryException(f"خطأ في تنفيذ الاستعلام: {e}")
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """
        تنفيذ استعلام متعدد
        Execute multiple queries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                affected_rows = cursor.rowcount
                logger.debug(f"تم تنفيذ {len(params_list)} استعلام وتأثر {affected_rows} صف")
                return affected_rows
        except sqlite3.IntegrityError as e:
            logger.error(f"خطأ في تكامل البيانات: {e}")
            raise DatabaseIntegrityException(f"خطأ في تكامل البيانات: {e}")
        except sqlite3.Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلامات المتعددة: {e}")
            raise DatabaseQueryException(f"خطأ في تنفيذ الاستعلامات المتعددة: {e}")
    
    def get_table_count(self, table_name: str) -> int:
        """
        الحصول على عدد السجلات في جدول
        Get record count in table
        """
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.execute_query(query)
        return result[0]["count"] if result else 0
    
    def table_exists(self, table_name: str) -> bool:
        """
        التحقق من وجود جدول
        Check if table exists
        """
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_query(query, (table_name,))
        return len(result) > 0
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        الحصول على معلومات قاعدة البيانات
        Get database information
        """
        info = {
            "path": self.db_path,
            "size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
            "tables": {},
            "created_at": datetime.fromtimestamp(os.path.getctime(self.db_path)) if os.path.exists(self.db_path) else None
        }
        
        # عدد السجلات في كل جدول
        tables = ["students", "absence_records", "school_info", "grades", 
                 "report_templates", "settings", "operation_log", "backups", "statistics"]
        
        for table in tables:
            if self.table_exists(table):
                info["tables"][table] = self.get_table_count(table)
        
        return info

    def create_backup(self, description: str = None) -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        Create database backup
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"attendance_backup_{timestamp}.db"
            backup_file_path = os.path.join(self.backup_path, backup_filename)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(self.db_path, backup_file_path)

            # تسجيل النسخة الاحتياطية في قاعدة البيانات
            file_size = os.path.getsize(backup_file_path)
            backup_id = self._generate_id()

            query = """
                INSERT INTO backups (id, file_path, file_size, description, is_automatic)
                VALUES (?, ?, ?, ?, ?)
            """
            self.execute_non_query(query, (backup_id, backup_file_path, file_size, description, True))

            logger.info(f"تم إنشاء نسخة احتياطية: {backup_file_path}")

            # تنظيف النسخ الاحتياطية القديمة
            self._cleanup_old_backups()

            return backup_file_path
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise DatabaseException(f"فشل في إنشاء النسخة الاحتياطية: {e}")

    def restore_backup(self, backup_file_path: str) -> bool:
        """
        استعادة نسخة احتياطية
        Restore database backup
        """
        try:
            if not os.path.exists(backup_file_path):
                raise DatabaseException("ملف النسخة الاحتياطية غير موجود")

            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            self.create_backup("نسخة احتياطية قبل الاستعادة")

            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_file_path, self.db_path)

            logger.info(f"تم استعادة النسخة الاحتياطية من: {backup_file_path}")
            return True
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            raise DatabaseException(f"فشل في استعادة النسخة الاحتياطية: {e}")

    def _cleanup_old_backups(self):
        """
        تنظيف النسخ الاحتياطية القديمة
        Cleanup old backups
        """
        try:
            max_backups = self.config.get("database.max_backups", 30)

            # الحصول على قائمة النسخ الاحتياطية مرتبة بالتاريخ
            query = """
                SELECT id, file_path FROM backups
                WHERE is_automatic = 1
                ORDER BY created_at DESC
            """
            backups = self.execute_query(query)

            # حذف النسخ الزائدة
            if len(backups) > max_backups:
                for backup in backups[max_backups:]:
                    try:
                        # حذف الملف
                        if os.path.exists(backup["file_path"]):
                            os.remove(backup["file_path"])

                        # حذف السجل من قاعدة البيانات
                        delete_query = "DELETE FROM backups WHERE id = ?"
                        self.execute_non_query(delete_query, (backup["id"],))

                        logger.debug(f"تم حذف النسخة الاحتياطية القديمة: {backup['file_path']}")
                    except Exception as e:
                        logger.warning(f"خطأ في حذف النسخة الاحتياطية {backup['file_path']}: {e}")
        except Exception as e:
            logger.warning(f"خطأ في تنظيف النسخ الاحتياطية: {e}")

    def _generate_id(self) -> str:
        """
        إنشاء معرف فريد
        Generate unique ID
        """
        import uuid
        return str(uuid.uuid4())

    def vacuum_database(self) -> bool:
        """
        ضغط قاعدة البيانات وتحسين الأداء
        Vacuum database for optimization
        """
        try:
            with self.get_connection() as conn:
                conn.execute("VACUUM")
                conn.commit()
            logger.info("تم ضغط قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"خطأ في ضغط قاعدة البيانات: {e}")
            return False

    def check_database_integrity(self) -> bool:
        """
        فحص تكامل قاعدة البيانات
        Check database integrity
        """
        try:
            result = self.execute_query("PRAGMA integrity_check")
            is_ok = len(result) == 1 and result[0][0] == "ok"

            if is_ok:
                logger.info("فحص تكامل قاعدة البيانات: سليمة")
            else:
                logger.warning("فحص تكامل قاعدة البيانات: توجد مشاكل")
                for row in result:
                    logger.warning(f"مشكلة في التكامل: {row[0]}")

            return is_ok
        except Exception as e:
            logger.error(f"خطأ في فحص تكامل قاعدة البيانات: {e}")
            return False

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات قاعدة البيانات
        Get database statistics
        """
        try:
            stats = {}

            # إحصائيات الطلاب
            stats["students"] = {
                "total": self.get_table_count("students"),
                "active": self.execute_query("SELECT COUNT(*) as count FROM students WHERE is_active = 1")[0]["count"],
                "by_grade": {}
            }

            # إحصائيات الطلاب حسب الصف
            grade_stats = self.execute_query("""
                SELECT grade_code, COUNT(*) as count
                FROM students
                WHERE is_active = 1
                GROUP BY grade_code
            """)
            for row in grade_stats:
                stats["students"]["by_grade"][row["grade_code"]] = row["count"]

            # إحصائيات الغياب
            stats["absences"] = {
                "total": self.get_table_count("absence_records"),
                "excused": self.execute_query("SELECT COUNT(*) as count FROM absence_records WHERE absence_type = 'EXCUSED'")[0]["count"],
                "unexcused": self.execute_query("SELECT COUNT(*) as count FROM absence_records WHERE absence_type = 'UNEXCUSED'")[0]["count"]
            }

            # إحصائيات النسخ الاحتياطية
            stats["backups"] = {
                "total": self.get_table_count("backups"),
                "automatic": self.execute_query("SELECT COUNT(*) as count FROM backups WHERE is_automatic = 1")[0]["count"]
            }

            return stats
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
            return {}

    def close(self):
        """
        إغلاق مدير قاعدة البيانات
        Close database manager
        """
        logger.info("تم إغلاق مدير قاعدة البيانات")
