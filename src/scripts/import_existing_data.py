"""
سكريبت استيراد البيانات الموجودة
Script to import existing student data from StudentGuidance.xls
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.database_manager import DatabaseManager
from services.excel_import_service import ExcelImportService
from utils.logger import get_logger
from utils.config import Config

logger = get_logger("ImportExistingData")


def main():
    """
    الدالة الرئيسية لاستيراد البيانات الموجودة
    Main function to import existing data
    """
    try:
        logger.info("بدء عملية استيراد البيانات الموجودة")
        
        # تهيئة إدارة قاعدة البيانات
        config = Config()
        db_manager = DatabaseManager(config)
        
        # تهيئة قاعدة البيانات
        logger.info("تهيئة قاعدة البيانات...")
        db_manager.initialize_database()
        
        # تهيئة خدمة الاستيراد
        import_service = ExcelImportService(db_manager)
        
        # مسار ملف البيانات الموجودة
        data_file_path = project_root.parent / "StudentGuidance.xls"
        
        if not data_file_path.exists():
            logger.error(f"ملف البيانات غير موجود: {data_file_path}")
            return False
        
        logger.info(f"ملف البيانات موجود: {data_file_path}")
        
        # معاينة البيانات أولاً
        logger.info("معاينة البيانات...")
        preview_result = import_service.preview_excel_import(str(data_file_path))
        
        if not preview_result["success"]:
            logger.error("فشل في معاينة البيانات")
            return False
        
        preview_data = preview_result["data"]
        logger.info(f"تم العثور على {preview_data['file_info']['total_rows']} صف في الملف")
        logger.info(f"الأعمدة المتاحة: {preview_data['available_columns']}")
        
        # تحديد تطابق الأعمدة للملف الحالي
        column_mapping = {
            "name": "اسم الطالب",
            "civil_id": "رقم الهوية",
            "grade_code": "الصف",
            "phone": "رقم الجوال",
            "parent_phone": "جوال ولي الأمر",
            "address": "العنوان"
        }
        
        # التحقق من وجود الأعمدة المطلوبة
        available_columns = preview_data['available_columns']
        missing_columns = []
        
        for field, column in column_mapping.items():
            if column not in available_columns:
                # البحث عن أعمدة مشابهة
                similar_columns = [col for col in available_columns if any(word in col for word in column.split())]
                if similar_columns:
                    logger.info(f"استخدام العمود '{similar_columns[0]}' بدلاً من '{column}' للحقل '{field}'")
                    column_mapping[field] = similar_columns[0]
                else:
                    missing_columns.append(column)
        
        if missing_columns:
            logger.warning(f"أعمدة مفقودة: {missing_columns}")
        
        # تحديث تطابق الأعمدة بناءً على الأعمدة المتاحة
        final_mapping = {}
        for field, column in column_mapping.items():
            if column in available_columns:
                final_mapping[field] = column
        
        logger.info(f"تطابق الأعمدة النهائي: {final_mapping}")
        
        # التحقق من صحة البيانات
        logger.info("التحقق من صحة البيانات...")
        validation_result = import_service.validate_excel_data(str(data_file_path), column_mapping=final_mapping)
        
        if not validation_result["success"]:
            logger.error("فشل في التحقق من صحة البيانات")
            return False
        
        validation_data = validation_result["data"]
        if not validation_data["is_valid"]:
            logger.warning(f"تم العثور على {validation_data['summary']['error_count']} خطأ في البيانات")
            for error in validation_data["errors"][:10]:  # عرض أول 10 أخطاء
                logger.warning(f"خطأ: {error}")
        
        # استيراد البيانات
        logger.info("بدء استيراد البيانات...")
        import_result = import_service.import_students_from_excel(
            file_path=str(data_file_path),
            column_mapping=final_mapping,
            skip_duplicates=True,
            validate_data=True,
            imported_by="system_import"
        )
        
        if not import_result["success"]:
            logger.error("فشل في استيراد البيانات")
            return False
        
        # عرض نتائج الاستيراد
        import_data = import_result["data"]
        logger.info("=== نتائج الاستيراد ===")
        logger.info(f"إجمالي الصفوف المعالجة: {import_data['total_processed']}")
        logger.info(f"تم استيراد بنجاح: {import_data['successful_count']} طالب")
        logger.info(f"فشل في الاستيراد: {import_data['failed_count']} طالب")
        logger.info(f"تم تخطي (مكرر): {import_data['skipped_count']} طالب")
        
        # عرض تفاصيل الأخطاء إن وجدت
        if import_data['failed_count'] > 0:
            logger.warning("تفاصيل الأخطاء:")
            for failed in import_data['failed_imports'][:5]:  # عرض أول 5 أخطاء
                logger.warning(f"فشل: {failed['student']['name']} - {failed['reason']}")
        
        # عرض تفاصيل المتخطين إن وجدوا
        if import_data['skipped_count'] > 0:
            logger.info("تفاصيل المتخطين:")
            for skipped in import_data['skipped_duplicates'][:5]:  # عرض أول 5 متخطين
                logger.info(f"متخطي: {skipped['student']['name']} - {skipped['reason']}")
        
        # إنشاء القوالب الافتراضية للتقارير
        logger.info("إنشاء القوالب الافتراضية للتقارير...")
        from repositories.report_repository import ReportRepository
        report_repo = ReportRepository(db_manager)
        
        if report_repo.create_default_templates():
            logger.info("تم إنشاء القوالب الافتراضية بنجاح")
        else:
            logger.warning("فشل في إنشاء القوالب الافتراضية")
        
        # إنشاء إحصائيات أولية
        logger.info("إنشاء إحصائيات أولية...")
        create_initial_statistics(db_manager)
        
        logger.info("تم الانتهاء من استيراد البيانات الموجودة بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في استيراد البيانات الموجودة: {e}")
        return False
    finally:
        # إغلاق اتصال قاعدة البيانات
        if 'db_manager' in locals():
            db_manager.close()


def create_initial_statistics(db_manager: DatabaseManager):
    """
    إنشاء إحصائيات أولية
    Create initial statistics
    """
    try:
        from repositories.student_repository import StudentRepository
        
        student_repo = StudentRepository(db_manager)
        
        # إحصائيات الطلاب
        total_students = len(student_repo.get_all())
        active_students = len(student_repo.get_active_students())
        
        # إحصائيات حسب الصف
        grade_stats = {}
        for grade in ["1A", "1B", "2A", "2B", "3A", "3B"]:
            grade_students = student_repo.get_by_grade(grade)
            grade_stats[grade] = len(grade_students)
        
        logger.info("=== الإحصائيات الأولية ===")
        logger.info(f"إجمالي الطلاب: {total_students}")
        logger.info(f"الطلاب النشطون: {active_students}")
        logger.info("إحصائيات الصفوف:")
        for grade, count in grade_stats.items():
            logger.info(f"  {grade}: {count} طالب")
        
        # حفظ الإحصائيات في قاعدة البيانات
        stats_data = {
            "total_students": total_students,
            "active_students": active_students,
            "grade_statistics": grade_stats,
            "last_updated": "2024-01-01"  # يمكن تحديثها لاحقاً
        }
        
        # يمكن إضافة حفظ الإحصائيات في جدول منفصل هنا
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الإحصائيات الأولية: {e}")


def test_database_connection():
    """
    اختبار اتصال قاعدة البيانات
    Test database connection
    """
    try:
        config = Config()
        db_manager = DatabaseManager(config)
        
        # اختبار الاتصال
        with db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                logger.info("اتصال قاعدة البيانات يعمل بشكل صحيح")
                return True
        
        return False
        
    except Exception as e:
        logger.error(f"خطأ في اختبار اتصال قاعدة البيانات: {e}")
        return False


if __name__ == "__main__":
    # اختبار اتصال قاعدة البيانات أولاً
    if not test_database_connection():
        logger.error("فشل في اتصال قاعدة البيانات")
        sys.exit(1)
    
    # تشغيل عملية الاستيراد
    success = main()
    
    if success:
        logger.info("تم الانتهاء من عملية الاستيراد بنجاح")
        sys.exit(0)
    else:
        logger.error("فشلت عملية الاستيراد")
        sys.exit(1)
