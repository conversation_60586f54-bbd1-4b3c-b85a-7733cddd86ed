"""
مستودع الطلاب
Student repository for data access operations
"""

from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from repositories.base_repository import BaseRepository
    from models.student import Student
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import DatabaseException, DuplicateStudentException, ValidationException
except ImportError:
    try:
        from src.repositories.base_repository import BaseRepository
        from src.models.student import Student
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import DatabaseException, DuplicateStudentException, ValidationException
    except ImportError:
        # Fallback for relative imports
        from .base_repository import BaseRepository
        from ..models.student import Student
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import DatabaseException, DuplicateStudentException, ValidationException

logger = get_logger("StudentRepository")


class StudentRepository(BaseRepository):
    """
    مستودع الطلاب
    Student repository
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
    
    def get_table_name(self) -> str:
        """الحصول على اسم الجدول"""
        return "students"
    
    def map_row_to_model(self, row: Dict[str, Any]) -> Student:
        """
        تحويل صف قاعدة البيانات إلى نموذج طالب
        Map database row to student model
        """
        student = Student()
        student.from_dict(row)
        return student
    
    def map_model_to_params(self, model: Student) -> Tuple:
        """
        تحويل نموذج الطالب إلى معاملات قاعدة البيانات
        Map student model to database parameters
        """
        return (
            model.id,
            model.civil_id,
            model.name,
            model.phone,
            model.grade_code,
            model.semester,
            model.is_active,
            model.notes,
            model.created_at.isoformat() if model.created_at else None,
            model.updated_at.isoformat() if model.updated_at else None
        )
    
    def get_insert_columns(self) -> str:
        """الحصول على أعمدة الإدراج"""
        return "id, civil_id, name, phone, grade_code, semester, is_active, notes, created_at, updated_at"
    
    def get_update_set_clause(self) -> str:
        """الحصول على جملة SET للتحديث"""
        return "civil_id = ?, name = ?, phone = ?, grade_code = ?, semester = ?, is_active = ?, notes = ?, updated_at = ?"
    
    def map_model_to_params(self, model: Student) -> Tuple:
        """تحويل النموذج إلى معاملات للتحديث"""
        if hasattr(self, '_is_update') and self._is_update:
            return (
                model.civil_id,
                model.name,
                model.phone,
                model.grade_code,
                model.semester,
                model.is_active,
                model.notes,
                model.updated_at.isoformat() if model.updated_at else None
            )
        else:
            return (
                model.id,
                model.civil_id,
                model.name,
                model.phone,
                model.grade_code,
                model.semester,
                model.is_active,
                model.notes,
                model.created_at.isoformat() if model.created_at else None,
                model.updated_at.isoformat() if model.updated_at else None
            )
    
    def update(self, model: Student) -> bool:
        """تحديث طالب مع معالجة خاصة للمعاملات"""
        self._is_update = True
        try:
            return super().update(model)
        finally:
            self._is_update = False
    
    def get_by_civil_id(self, civil_id: str) -> Optional[Student]:
        """
        الحصول على طالب برقم الهوية المدنية
        Get student by civil ID
        """
        try:
            query = "SELECT * FROM students WHERE civil_id = ?"
            results = self.db_manager.execute_query(query, (civil_id,))
            
            if results:
                return self.map_row_to_model(dict(results[0]))
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على الطالب برقم الهوية {civil_id}: {e}")
            raise DatabaseException(f"خطأ في الحصول على الطالب: {e}")
    
    def get_by_grade(self, grade_code: str, active_only: bool = True) -> List[Student]:
        """
        الحصول على طلاب صف معين
        Get students by grade
        """
        try:
            query = "SELECT * FROM students WHERE grade_code = ?"
            params = [grade_code]
            
            if active_only:
                query += " AND is_active = 1"
            
            query += " ORDER BY name"
            
            results = self.db_manager.execute_query(query, tuple(params))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على طلاب الصف {grade_code}: {e}")
            raise DatabaseException(f"خطأ في الحصول على طلاب الصف: {e}")
    
    def get_active_students(self) -> List[Student]:
        """
        الحصول على الطلاب النشطين
        Get active students
        """
        try:
            query = "SELECT * FROM students WHERE is_active = 1 ORDER BY grade_code, name"
            results = self.db_manager.execute_query(query)
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على الطلاب النشطين: {e}")
            raise DatabaseException(f"خطأ في الحصول على الطلاب النشطين: {e}")
    
    def search_students(self, search_term: str, grade_code: str = None, 
                       active_only: bool = True) -> List[Student]:
        """
        البحث في الطلاب
        Search students
        """
        try:
            if not search_term:
                return []
            
            # إنشاء شروط البحث
            where_conditions = ["(name LIKE ? OR civil_id LIKE ?)"]
            params = [f"%{search_term}%", f"%{search_term}%"]
            
            if grade_code:
                where_conditions.append("grade_code = ?")
                params.append(grade_code)
            
            if active_only:
                where_conditions.append("is_active = 1")
            
            where_clause = " AND ".join(where_conditions)
            query = f"SELECT * FROM students WHERE {where_clause} ORDER BY name"
            
            results = self.db_manager.execute_query(query, tuple(params))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في البحث عن الطلاب: {e}")
            raise DatabaseException(f"خطأ في البحث عن الطلاب: {e}")
    
    def check_civil_id_exists(self, civil_id: str, exclude_id: str = None) -> bool:
        """
        التحقق من وجود رقم هوية مدنية
        Check if civil ID exists
        """
        try:
            query = "SELECT 1 FROM students WHERE civil_id = ?"
            params = [civil_id]
            
            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)
            
            results = self.db_manager.execute_query(query, tuple(params))
            return len(results) > 0
        except Exception as e:
            logger.error(f"خطأ في التحقق من رقم الهوية {civil_id}: {e}")
            return False
    
    def insert(self, model: Student) -> bool:
        """
        إدراج طالب جديد مع التحقق من التكرار
        Insert new student with duplicate check
        """
        try:
            # التحقق من عدم تكرار رقم الهوية المدنية
            if self.check_civil_id_exists(model.civil_id):
                raise DuplicateStudentException(f"رقم الهوية المدنية {model.civil_id} موجود مسبقاً")
            
            return super().insert(model)
        except DuplicateStudentException:
            raise
        except Exception as e:
            logger.error(f"خطأ في إدراج الطالب: {e}")
            raise DatabaseException(f"خطأ في إدراج الطالب: {e}")
    
    def update(self, model: Student) -> bool:
        """
        تحديث طالب مع التحقق من التكرار
        Update student with duplicate check
        """
        try:
            # التحقق من عدم تكرار رقم الهوية المدنية
            if self.check_civil_id_exists(model.civil_id, model.id):
                raise DuplicateStudentException(f"رقم الهوية المدنية {model.civil_id} موجود مسبقاً")
            
            self._is_update = True
            return super().update(model)
        except DuplicateStudentException:
            raise
        except Exception as e:
            logger.error(f"خطأ في تحديث الطالب: {e}")
            raise DatabaseException(f"خطأ في تحديث الطالب: {e}")
        finally:
            self._is_update = False
    
    def activate_student(self, student_id: str) -> bool:
        """
        تفعيل طالب
        Activate student
        """
        try:
            query = "UPDATE students SET is_active = 1, updated_at = ? WHERE id = ?"
            params = (datetime.now().isoformat(), student_id)
            affected_rows = self.db_manager.execute_non_query(query, params)
            
            if affected_rows > 0:
                logger.info(f"تم تفعيل الطالب: {student_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تفعيل الطالب {student_id}: {e}")
            raise DatabaseException(f"خطأ في تفعيل الطالب: {e}")
    
    def deactivate_student(self, student_id: str) -> bool:
        """
        إلغاء تفعيل طالب
        Deactivate student
        """
        try:
            query = "UPDATE students SET is_active = 0, updated_at = ? WHERE id = ?"
            params = (datetime.now().isoformat(), student_id)
            affected_rows = self.db_manager.execute_non_query(query, params)
            
            if affected_rows > 0:
                logger.info(f"تم إلغاء تفعيل الطالب: {student_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في إلغاء تفعيل الطالب {student_id}: {e}")
            raise DatabaseException(f"خطأ في إلغاء تفعيل الطالب: {e}")
    
    def get_students_count_by_grade(self) -> Dict[str, int]:
        """
        الحصول على عدد الطلاب حسب الصف
        Get students count by grade
        """
        try:
            query = """
                SELECT grade_code, COUNT(*) as count 
                FROM students 
                WHERE is_active = 1 
                GROUP BY grade_code
            """
            results = self.db_manager.execute_query(query)
            
            count_dict = {}
            for row in results:
                count_dict[row["grade_code"]] = row["count"]
            
            return count_dict
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الطلاب: {e}")
            raise DatabaseException(f"خطأ في الحصول على إحصائيات الطلاب: {e}")
    
    def bulk_insert(self, students: List[Student]) -> int:
        """
        إدراج عدة طلاب دفعة واحدة
        Bulk insert students
        """
        try:
            if not students:
                return 0
            
            # التحقق من صحة جميع الطلاب
            for student in students:
                if not student.validate():
                    raise ValidationException(f"بيانات طالب غير صحيحة: {student.get_error_message()}")
                
                # تعيين المعرف والطوابع الزمنية
                if not student.id:
                    student.id = student.generate_id()
                student.set_timestamps(is_new=True)
            
            # إعداد الاستعلام
            query = f"INSERT INTO {self.table_name} ({self.get_insert_columns()}) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            params_list = [self.map_model_to_params(student) for student in students]
            
            # تنفيذ الإدراج المتعدد
            affected_rows = self.db_manager.execute_many(query, params_list)
            
            logger.info(f"تم إدراج {affected_rows} طالب بنجاح")
            return affected_rows
        except Exception as e:
            logger.error(f"خطأ في الإدراج المتعدد للطلاب: {e}")
            raise DatabaseException(f"خطأ في الإدراج المتعدد للطلاب: {e}")
