"""
المستودع الأساسي
Base repository for data access operations
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

from ..data.database_manager import DatabaseManager
from ..utils.logger import get_logger
from ..utils.exceptions import DatabaseException, ValidationException

logger = get_logger("BaseRepository")


class BaseRepository(ABC):
    """
    المستودع الأساسي لعمليات الوصول للبيانات
    Base repository for data access operations
    """
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.table_name = self.get_table_name()
    
    @abstractmethod
    def get_table_name(self) -> str:
        """
        الحصول على اسم الجدول
        Get table name
        """
        pass
    
    @abstractmethod
    def map_row_to_model(self, row: Dict[str, Any]) -> Any:
        """
        تحويل صف قاعدة البيانات إلى نموذج
        Map database row to model
        """
        pass
    
    @abstractmethod
    def map_model_to_params(self, model: Any) -> Tuple:
        """
        تحويل النموذج إلى معاملات قاعدة البيانات
        Map model to database parameters
        """
        pass
    
    def get_by_id(self, record_id: str) -> Optional[Any]:
        """
        الحصول على سجل بالمعرف
        Get record by ID
        """
        try:
            query = f"SELECT * FROM {self.table_name} WHERE id = ?"
            results = self.db_manager.execute_query(query, (record_id,))
            
            if results:
                return self.map_row_to_model(dict(results[0]))
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على السجل {record_id}: {e}")
            raise DatabaseException(f"خطأ في الحصول على السجل: {e}")
    
    def get_all(self, limit: int = None, offset: int = 0) -> List[Any]:
        """
        الحصول على جميع السجلات
        Get all records
        """
        try:
            query = f"SELECT * FROM {self.table_name} ORDER BY created_at DESC"
            if limit:
                query += f" LIMIT {limit} OFFSET {offset}"
            
            results = self.db_manager.execute_query(query)
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على السجلات: {e}")
            raise DatabaseException(f"خطأ في الحصول على السجلات: {e}")
    
    def count(self, where_clause: str = None, params: Tuple = ()) -> int:
        """
        عد السجلات
        Count records
        """
        try:
            query = f"SELECT COUNT(*) as count FROM {self.table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            results = self.db_manager.execute_query(query, params)
            return results[0]["count"] if results else 0
        except Exception as e:
            logger.error(f"خطأ في عد السجلات: {e}")
            raise DatabaseException(f"خطأ في عد السجلات: {e}")
    
    def exists(self, record_id: str) -> bool:
        """
        التحقق من وجود سجل
        Check if record exists
        """
        try:
            query = f"SELECT 1 FROM {self.table_name} WHERE id = ? LIMIT 1"
            results = self.db_manager.execute_query(query, (record_id,))
            return len(results) > 0
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود السجل {record_id}: {e}")
            return False
    
    def insert(self, model: Any) -> bool:
        """
        إدراج سجل جديد
        Insert new record
        """
        try:
            # التحقق من صحة النموذج
            if not model.validate():
                raise ValidationException(f"بيانات غير صحيحة: {model.get_error_message()}")
            
            # تعيين المعرف والطوابع الزمنية
            if not model.id:
                model.id = model.generate_id()
            model.set_timestamps(is_new=True)
            
            # تحويل النموذج إلى معاملات
            params = self.map_model_to_params(model)
            
            # إنشاء استعلام الإدراج
            placeholders = ", ".join(["?" for _ in params])
            columns = self.get_insert_columns()
            query = f"INSERT INTO {self.table_name} ({columns}) VALUES ({placeholders})"
            
            # تنفيذ الاستعلام
            affected_rows = self.db_manager.execute_non_query(query, params)
            
            if affected_rows > 0:
                logger.info(f"تم إدراج سجل جديد في {self.table_name}: {model.id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في إدراج السجل: {e}")
            raise DatabaseException(f"خطأ في إدراج السجل: {e}")
    
    def update(self, model: Any) -> bool:
        """
        تحديث سجل موجود
        Update existing record
        """
        try:
            # التحقق من صحة النموذج
            if not model.validate():
                raise ValidationException(f"بيانات غير صحيحة: {model.get_error_message()}")
            
            # التحقق من وجود السجل
            if not self.exists(model.id):
                raise DatabaseException("السجل غير موجود")
            
            # تحديث الطابع الزمني
            model.set_timestamps(is_new=False)
            
            # تحويل النموذج إلى معاملات
            params = self.map_model_to_params(model)
            
            # إنشاء استعلام التحديث
            set_clause = self.get_update_set_clause()
            query = f"UPDATE {self.table_name} SET {set_clause} WHERE id = ?"
            
            # إضافة معرف السجل في النهاية
            params = params + (model.id,)
            
            # تنفيذ الاستعلام
            affected_rows = self.db_manager.execute_non_query(query, params)
            
            if affected_rows > 0:
                logger.info(f"تم تحديث السجل في {self.table_name}: {model.id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تحديث السجل: {e}")
            raise DatabaseException(f"خطأ في تحديث السجل: {e}")
    
    def delete(self, record_id: str) -> bool:
        """
        حذف سجل
        Delete record
        """
        try:
            # التحقق من وجود السجل
            if not self.exists(record_id):
                raise DatabaseException("السجل غير موجود")
            
            query = f"DELETE FROM {self.table_name} WHERE id = ?"
            affected_rows = self.db_manager.execute_non_query(query, (record_id,))
            
            if affected_rows > 0:
                logger.info(f"تم حذف السجل من {self.table_name}: {record_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف السجل: {e}")
            raise DatabaseException(f"خطأ في حذف السجل: {e}")
    
    def delete_many(self, record_ids: List[str]) -> int:
        """
        حذف عدة سجلات
        Delete multiple records
        """
        try:
            if not record_ids:
                return 0
            
            placeholders = ", ".join(["?" for _ in record_ids])
            query = f"DELETE FROM {self.table_name} WHERE id IN ({placeholders})"
            affected_rows = self.db_manager.execute_non_query(query, tuple(record_ids))
            
            logger.info(f"تم حذف {affected_rows} سجل من {self.table_name}")
            return affected_rows
        except Exception as e:
            logger.error(f"خطأ في حذف السجلات المتعددة: {e}")
            raise DatabaseException(f"خطأ في حذف السجلات المتعددة: {e}")
    
    def search(self, search_term: str, search_columns: List[str], 
              limit: int = None, offset: int = 0) -> List[Any]:
        """
        البحث في السجلات
        Search records
        """
        try:
            if not search_term or not search_columns:
                return []
            
            # إنشاء شروط البحث
            search_conditions = []
            params = []
            
            for column in search_columns:
                search_conditions.append(f"{column} LIKE ?")
                params.append(f"%{search_term}%")
            
            where_clause = " OR ".join(search_conditions)
            query = f"SELECT * FROM {self.table_name} WHERE {where_clause} ORDER BY created_at DESC"
            
            if limit:
                query += f" LIMIT {limit} OFFSET {offset}"
            
            results = self.db_manager.execute_query(query, tuple(params))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في البحث: {e}")
            raise DatabaseException(f"خطأ في البحث: {e}")
    
    def get_by_field(self, field_name: str, field_value: Any) -> List[Any]:
        """
        الحصول على السجلات بحقل معين
        Get records by specific field
        """
        try:
            query = f"SELECT * FROM {self.table_name} WHERE {field_name} = ? ORDER BY created_at DESC"
            results = self.db_manager.execute_query(query, (field_value,))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على السجلات بالحقل {field_name}: {e}")
            raise DatabaseException(f"خطأ في الحصول على السجلات: {e}")
    
    @abstractmethod
    def get_insert_columns(self) -> str:
        """
        الحصول على أعمدة الإدراج
        Get insert columns
        """
        pass
    
    @abstractmethod
    def get_update_set_clause(self) -> str:
        """
        الحصول على جملة SET للتحديث
        Get update SET clause
        """
        pass
