"""
مستودع التقارير
Report repository for data access operations
"""

from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from repositories.base_repository import BaseRepository
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import DatabaseException
except ImportError:
    try:
        from src.repositories.base_repository import BaseRepository
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import DatabaseException
    except ImportError:
        # Fallback for relative imports
        from .base_repository import BaseRepository
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import DatabaseException

logger = get_logger("ReportRepository")


class ReportTemplate:
    """
    نموذج قالب التقرير
    Report template model
    """
    
    def __init__(self):
        self.id: str = ""
        self.name: str = ""
        self.description: str = ""
        self.template_type: str = ""  # DAILY, WEEKLY, MONTHLY, CUSTOM
        self.template_content: str = ""  # JSON content
        self.is_default: bool = False
        self.created_at: datetime = None
        self.updated_at: datetime = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "template_type": self.template_type,
            "template_content": self.template_content,
            "is_default": self.is_default,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """تحويل من قاموس"""
        self.id = data.get("id", "")
        self.name = data.get("name", "")
        self.description = data.get("description", "")
        self.template_type = data.get("template_type", "")
        self.template_content = data.get("template_content", "")
        self.is_default = bool(data.get("is_default", False))
        
        if data.get("created_at"):
            self.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("updated_at"):
            self.updated_at = datetime.fromisoformat(data["updated_at"])


class ReportRepository(BaseRepository):
    """
    مستودع التقارير
    Report repository
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
    
    def get_table_name(self) -> str:
        """الحصول على اسم الجدول"""
        return "report_templates"
    
    def map_row_to_model(self, row: Dict[str, Any]) -> ReportTemplate:
        """
        تحويل صف قاعدة البيانات إلى نموذج قالب تقرير
        Map database row to report template model
        """
        template = ReportTemplate()
        template.from_dict(row)
        return template
    
    def map_model_to_params(self, model: ReportTemplate) -> Tuple:
        """
        تحويل نموذج قالب التقرير إلى معاملات قاعدة البيانات
        Map report template model to database parameters
        """
        if hasattr(self, '_is_update') and self._is_update:
            return (
                model.name,
                model.description,
                model.template_type,
                model.template_content,
                model.is_default,
                model.updated_at.isoformat() if model.updated_at else None
            )
        else:
            return (
                model.id,
                model.name,
                model.description,
                model.template_type,
                model.template_content,
                model.is_default,
                model.created_at.isoformat() if model.created_at else None,
                model.updated_at.isoformat() if model.updated_at else None
            )
    
    def get_insert_columns(self) -> str:
        """الحصول على أعمدة الإدراج"""
        return "id, name, description, template_type, template_content, is_default, created_at, updated_at"
    
    def get_update_set_clause(self) -> str:
        """الحصول على جملة SET للتحديث"""
        return "name = ?, description = ?, template_type = ?, template_content = ?, is_default = ?, updated_at = ?"
    
    def update(self, model: ReportTemplate) -> bool:
        """تحديث قالب تقرير مع معالجة خاصة للمعاملات"""
        self._is_update = True
        try:
            return super().update(model)
        finally:
            self._is_update = False
    
    def get_by_type(self, template_type: str) -> List[ReportTemplate]:
        """
        الحصول على قوالب التقارير حسب النوع
        Get report templates by type
        """
        try:
            query = "SELECT * FROM report_templates WHERE template_type = ? ORDER BY name"
            results = self.db_manager.execute_query(query, (template_type,))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على قوالب التقارير من النوع {template_type}: {e}")
            raise DatabaseException(f"خطأ في الحصول على قوالب التقارير: {e}")
    
    def get_default_templates(self) -> List[ReportTemplate]:
        """
        الحصول على القوالب الافتراضية
        Get default templates
        """
        try:
            query = "SELECT * FROM report_templates WHERE is_default = 1 ORDER BY template_type, name"
            results = self.db_manager.execute_query(query)
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على القوالب الافتراضية: {e}")
            raise DatabaseException(f"خطأ في الحصول على القوالب الافتراضية: {e}")
    
    def get_custom_templates(self) -> List[ReportTemplate]:
        """
        الحصول على القوالب المخصصة
        Get custom templates
        """
        try:
            query = "SELECT * FROM report_templates WHERE is_default = 0 ORDER BY created_at DESC"
            results = self.db_manager.execute_query(query)
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على القوالب المخصصة: {e}")
            raise DatabaseException(f"خطأ في الحصول على القوالب المخصصة: {e}")
    
    def set_as_default(self, template_id: str, template_type: str) -> bool:
        """
        تعيين قالب كافتراضي لنوع معين
        Set template as default for specific type
        """
        try:
            # إلغاء الافتراضية من جميع القوالب من نفس النوع
            query1 = "UPDATE report_templates SET is_default = 0 WHERE template_type = ?"
            self.db_manager.execute_non_query(query1, (template_type,))
            
            # تعيين القالب المحدد كافتراضي
            query2 = "UPDATE report_templates SET is_default = 1, updated_at = ? WHERE id = ?"
            params = (datetime.now().isoformat(), template_id)
            affected_rows = self.db_manager.execute_non_query(query2, params)
            
            if affected_rows > 0:
                logger.info(f"تم تعيين القالب {template_id} كافتراضي للنوع {template_type}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تعيين القالب الافتراضي: {e}")
            raise DatabaseException(f"خطأ في تعيين القالب الافتراضي: {e}")
    
    def create_default_templates(self) -> bool:
        """
        إنشاء القوالب الافتراضية
        Create default templates
        """
        try:
            default_templates = [
                {
                    "name": "تقرير الغياب اليومي",
                    "description": "تقرير يومي لغياب الطلاب",
                    "template_type": "DAILY",
                    "template_content": """{
                        "title": "تقرير الغياب اليومي",
                        "sections": [
                            {"type": "header", "content": "بيانات المدرسة"},
                            {"type": "date", "content": "التاريخ"},
                            {"type": "table", "content": "جدول الغياب"},
                            {"type": "summary", "content": "ملخص الإحصائيات"},
                            {"type": "footer", "content": "التوقيعات"}
                        ]
                    }""",
                    "is_default": True
                },
                {
                    "name": "تقرير الغياب الأسبوعي",
                    "description": "تقرير أسبوعي لغياب الطلاب",
                    "template_type": "WEEKLY",
                    "template_content": """{
                        "title": "تقرير الغياب الأسبوعي",
                        "sections": [
                            {"type": "header", "content": "بيانات المدرسة"},
                            {"type": "date_range", "content": "الفترة الزمنية"},
                            {"type": "summary_table", "content": "ملخص الغياب"},
                            {"type": "detailed_table", "content": "تفاصيل الغياب"},
                            {"type": "statistics", "content": "الإحصائيات"},
                            {"type": "footer", "content": "التوقيعات"}
                        ]
                    }""",
                    "is_default": True
                },
                {
                    "name": "تقرير الغياب الشهري",
                    "description": "تقرير شهري لغياب الطلاب",
                    "template_type": "MONTHLY",
                    "template_content": """{
                        "title": "تقرير الغياب الشهري",
                        "sections": [
                            {"type": "header", "content": "بيانات المدرسة"},
                            {"type": "month_year", "content": "الشهر والسنة"},
                            {"type": "monthly_summary", "content": "ملخص شهري"},
                            {"type": "grade_statistics", "content": "إحصائيات الصفوف"},
                            {"type": "student_ranking", "content": "ترتيب الطلاب"},
                            {"type": "recommendations", "content": "التوصيات"},
                            {"type": "footer", "content": "التوقيعات"}
                        ]
                    }""",
                    "is_default": True
                }
            ]
            
            created_count = 0
            for template_data in default_templates:
                # التحقق من عدم وجود القالب
                existing = self.get_by_type(template_data["template_type"])
                default_exists = any(t.is_default for t in existing)
                
                if not default_exists:
                    template = ReportTemplate()
                    template.id = self._generate_id()
                    template.name = template_data["name"]
                    template.description = template_data["description"]
                    template.template_type = template_data["template_type"]
                    template.template_content = template_data["template_content"]
                    template.is_default = template_data["is_default"]
                    template.created_at = datetime.now()
                    template.updated_at = datetime.now()
                    
                    if self.insert(template):
                        created_count += 1
            
            logger.info(f"تم إنشاء {created_count} قالب افتراضي")
            return created_count > 0
        except Exception as e:
            logger.error(f"خطأ في إنشاء القوالب الافتراضية: {e}")
            raise DatabaseException(f"خطأ في إنشاء القوالب الافتراضية: {e}")
    
    def _generate_id(self) -> str:
        """إنشاء معرف فريد"""
        import uuid
        return str(uuid.uuid4())
    
    def insert(self, model: ReportTemplate) -> bool:
        """إدراج قالب تقرير جديد"""
        try:
            # تعيين المعرف والطوابع الزمنية
            if not model.id:
                model.id = self._generate_id()
            if not model.created_at:
                model.created_at = datetime.now()
            if not model.updated_at:
                model.updated_at = datetime.now()
            
            return super().insert(model)
        except Exception as e:
            logger.error(f"خطأ في إدراج قالب التقرير: {e}")
            raise DatabaseException(f"خطأ في إدراج قالب التقرير: {e}")
    
    def validate_template_content(self, template_content: str) -> bool:
        """
        التحقق من صحة محتوى القالب
        Validate template content
        """
        try:
            import json
            content = json.loads(template_content)
            
            # التحقق من وجود العناصر المطلوبة
            required_fields = ["title", "sections"]
            for field in required_fields:
                if field not in content:
                    return False
            
            # التحقق من صحة الأقسام
            if not isinstance(content["sections"], list):
                return False
            
            for section in content["sections"]:
                if not isinstance(section, dict) or "type" not in section or "content" not in section:
                    return False
            
            return True
        except (json.JSONDecodeError, Exception):
            return False
