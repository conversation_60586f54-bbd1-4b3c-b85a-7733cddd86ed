"""
مستودع سجلات الغياب
Absence records repository for data access operations
"""

from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, date

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from repositories.base_repository import BaseRepository
    from models.absence_record import AbsenceRecord
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import DatabaseException, DuplicateAbsenceException
except ImportError:
    try:
        from src.repositories.base_repository import BaseRepository
        from src.models.absence_record import AbsenceRecord
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import DatabaseException, DuplicateAbsenceException
    except ImportError:
        # Fallback for relative imports
        from .base_repository import BaseRepository
        from ..models.absence_record import AbsenceRecord
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import DatabaseException, DuplicateAbsenceException

logger = get_logger("AbsenceRepository")


class AbsenceRepository(BaseRepository):
    """
    مستودع سجلات الغياب
    Absence records repository
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
    
    def get_table_name(self) -> str:
        """الحصول على اسم الجدول"""
        return "absence_records"
    
    def map_row_to_model(self, row: Dict[str, Any]) -> AbsenceRecord:
        """
        تحويل صف قاعدة البيانات إلى نموذج سجل غياب
        Map database row to absence record model
        """
        absence = AbsenceRecord()
        absence.from_dict(row)
        return absence
    
    def map_model_to_params(self, model: AbsenceRecord) -> Tuple:
        """
        تحويل نموذج سجل الغياب إلى معاملات قاعدة البيانات
        Map absence record model to database parameters
        """
        if hasattr(self, '_is_update') and self._is_update:
            return (
                model.student_id,
                model.absence_date.isoformat() if model.absence_date else None,
                model.absence_type,
                model.reason,
                model.period,
                model.semester,
                model.academic_year,
                model.recorded_by,
                model.notes,
                model.updated_at.isoformat() if model.updated_at else None
            )
        else:
            return (
                model.id,
                model.student_id,
                model.absence_date.isoformat() if model.absence_date else None,
                model.absence_type,
                model.reason,
                model.period,
                model.semester,
                model.academic_year,
                model.recorded_by,
                model.notes,
                model.created_at.isoformat() if model.created_at else None,
                model.updated_at.isoformat() if model.updated_at else None
            )
    
    def get_insert_columns(self) -> str:
        """الحصول على أعمدة الإدراج"""
        return "id, student_id, absence_date, absence_type, reason, period, semester, academic_year, recorded_by, notes, created_at, updated_at"
    
    def get_update_set_clause(self) -> str:
        """الحصول على جملة SET للتحديث"""
        return "student_id = ?, absence_date = ?, absence_type = ?, reason = ?, period = ?, semester = ?, academic_year = ?, recorded_by = ?, notes = ?, updated_at = ?"
    
    def update(self, model: AbsenceRecord) -> bool:
        """تحديث سجل غياب مع معالجة خاصة للمعاملات"""
        self._is_update = True
        try:
            return super().update(model)
        finally:
            self._is_update = False
    
    def get_by_student_and_date(self, student_id: str, absence_date: date, period: str = None) -> Optional[AbsenceRecord]:
        """
        الحصول على سجل غياب بالطالب والتاريخ
        Get absence record by student and date
        """
        try:
            query = "SELECT * FROM absence_records WHERE student_id = ? AND absence_date = ?"
            params = [student_id, absence_date.isoformat()]
            
            if period:
                query += " AND period = ?"
                params.append(period)
            
            results = self.db_manager.execute_query(query, tuple(params))
            
            if results:
                return self.map_row_to_model(dict(results[0]))
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على سجل الغياب: {e}")
            raise DatabaseException(f"خطأ في الحصول على سجل الغياب: {e}")
    
    def get_by_student(self, student_id: str, start_date: date = None, end_date: date = None) -> List[AbsenceRecord]:
        """
        الحصول على سجلات غياب طالب
        Get absence records for student
        """
        try:
            query = "SELECT * FROM absence_records WHERE student_id = ?"
            params = [student_id]
            
            if start_date:
                query += " AND absence_date >= ?"
                params.append(start_date.isoformat())
            
            if end_date:
                query += " AND absence_date <= ?"
                params.append(end_date.isoformat())
            
            query += " ORDER BY absence_date DESC"
            
            results = self.db_manager.execute_query(query, tuple(params))
            return [self.map_row_to_model(dict(row)) for row in results]
        except Exception as e:
            logger.error(f"خطأ في الحصول على سجلات غياب الطالب {student_id}: {e}")
            raise DatabaseException(f"خطأ في الحصول على سجلات غياب الطالب: {e}")
    
    def get_by_date_range(self, start_date: date, end_date: date, grade_code: str = None) -> List[AbsenceRecord]:
        """
        الحصول على سجلات الغياب في نطاق تاريخ
        Get absence records in date range
        """
        try:
            query = """
                SELECT ar.*, s.name as student_name, s.civil_id as student_civil_id, s.grade_code
                FROM absence_records ar
                JOIN students s ON ar.student_id = s.id
                WHERE ar.absence_date >= ? AND ar.absence_date <= ?
            """
            params = [start_date.isoformat(), end_date.isoformat()]
            
            if grade_code:
                query += " AND s.grade_code = ?"
                params.append(grade_code)
            
            query += " ORDER BY ar.absence_date DESC, s.name"
            
            results = self.db_manager.execute_query(query, tuple(params))
            absences = []
            
            for row in results:
                absence = self.map_row_to_model(dict(row))
                # إضافة بيانات الطالب
                absence.student_name = row["student_name"]
                absence.student_civil_id = row["student_civil_id"]
                absence.grade_code = row["grade_code"]
                absences.append(absence)
            
            return absences
        except Exception as e:
            logger.error(f"خطأ في الحصول على سجلات الغياب في النطاق: {e}")
            raise DatabaseException(f"خطأ في الحصول على سجلات الغياب: {e}")
    
    def get_by_grade_and_date(self, grade_code: str, absence_date: date) -> List[AbsenceRecord]:
        """
        الحصول على سجلات غياب صف في تاريخ معين
        Get absence records for grade on specific date
        """
        try:
            query = """
                SELECT ar.*, s.name as student_name, s.civil_id as student_civil_id
                FROM absence_records ar
                JOIN students s ON ar.student_id = s.id
                WHERE s.grade_code = ? AND ar.absence_date = ?
                ORDER BY s.name
            """
            params = (grade_code, absence_date.isoformat())
            
            results = self.db_manager.execute_query(query, params)
            absences = []
            
            for row in results:
                absence = self.map_row_to_model(dict(row))
                absence.student_name = row["student_name"]
                absence.student_civil_id = row["student_civil_id"]
                absence.grade_code = grade_code
                absences.append(absence)
            
            return absences
        except Exception as e:
            logger.error(f"خطأ في الحصول على سجلات غياب الصف {grade_code}: {e}")
            raise DatabaseException(f"خطأ في الحصول على سجلات غياب الصف: {e}")
    
    def check_absence_exists(self, student_id: str, absence_date: date, period: str = None, exclude_id: str = None) -> bool:
        """
        التحقق من وجود سجل غياب
        Check if absence record exists
        """
        try:
            query = "SELECT 1 FROM absence_records WHERE student_id = ? AND absence_date = ?"
            params = [student_id, absence_date.isoformat()]
            
            if period:
                query += " AND period = ?"
                params.append(period)
            
            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)
            
            results = self.db_manager.execute_query(query, tuple(params))
            return len(results) > 0
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود سجل الغياب: {e}")
            return False
    
    def insert(self, model: AbsenceRecord) -> bool:
        """
        إدراج سجل غياب جديد مع التحقق من التكرار
        Insert new absence record with duplicate check
        """
        try:
            # التحقق من عدم تكرار سجل الغياب
            if self.check_absence_exists(model.student_id, model.absence_date, model.period):
                raise DuplicateAbsenceException("سجل الغياب موجود مسبقاً لهذا اليوم والفترة")
            
            return super().insert(model)
        except DuplicateAbsenceException:
            raise
        except Exception as e:
            logger.error(f"خطأ في إدراج سجل الغياب: {e}")
            raise DatabaseException(f"خطأ في إدراج سجل الغياب: {e}")
    
    def update(self, model: AbsenceRecord) -> bool:
        """
        تحديث سجل غياب مع التحقق من التكرار
        Update absence record with duplicate check
        """
        try:
            # التحقق من عدم تكرار سجل الغياب
            if self.check_absence_exists(model.student_id, model.absence_date, model.period, model.id):
                raise DuplicateAbsenceException("سجل الغياب موجود مسبقاً لهذا اليوم والفترة")
            
            self._is_update = True
            return super().update(model)
        except DuplicateAbsenceException:
            raise
        except Exception as e:
            logger.error(f"خطأ في تحديث سجل الغياب: {e}")
            raise DatabaseException(f"خطأ في تحديث سجل الغياب: {e}")
        finally:
            self._is_update = False
    
    def get_absence_statistics(self, start_date: date, end_date: date, grade_code: str = None) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الغياب
        Get absence statistics
        """
        try:
            base_query = """
                SELECT 
                    COUNT(*) as total_absences,
                    COUNT(CASE WHEN absence_type = 'EXCUSED' THEN 1 END) as excused_absences,
                    COUNT(CASE WHEN absence_type = 'UNEXCUSED' THEN 1 END) as unexcused_absences,
                    COUNT(DISTINCT student_id) as students_with_absences
                FROM absence_records ar
                JOIN students s ON ar.student_id = s.id
                WHERE ar.absence_date >= ? AND ar.absence_date <= ?
            """
            params = [start_date.isoformat(), end_date.isoformat()]
            
            if grade_code:
                base_query += " AND s.grade_code = ?"
                params.append(grade_code)
            
            results = self.db_manager.execute_query(base_query, tuple(params))
            
            if results:
                stats = dict(results[0])
                
                # إحصائيات إضافية حسب التاريخ
                date_query = base_query.replace("COUNT(*) as total_absences,", "ar.absence_date,") + " GROUP BY ar.absence_date ORDER BY ar.absence_date"
                date_results = self.db_manager.execute_query(date_query, tuple(params))
                
                stats["by_date"] = {}
                for row in date_results:
                    stats["by_date"][row["absence_date"]] = {
                        "total": row["total_absences"],
                        "excused": row["excused_absences"],
                        "unexcused": row["unexcused_absences"]
                    }
                
                return stats
            
            return {
                "total_absences": 0,
                "excused_absences": 0,
                "unexcused_absences": 0,
                "students_with_absences": 0,
                "by_date": {}
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الغياب: {e}")
            raise DatabaseException(f"خطأ في الحصول على إحصائيات الغياب: {e}")
    
    def delete_by_student(self, student_id: str) -> int:
        """
        حذف جميع سجلات غياب طالب
        Delete all absence records for student
        """
        try:
            query = "DELETE FROM absence_records WHERE student_id = ?"
            affected_rows = self.db_manager.execute_non_query(query, (student_id,))
            
            logger.info(f"تم حذف {affected_rows} سجل غياب للطالب {student_id}")
            return affected_rows
        except Exception as e:
            logger.error(f"خطأ في حذف سجلات غياب الطالب {student_id}: {e}")
            raise DatabaseException(f"خطأ في حذف سجلات غياب الطالب: {e}")
