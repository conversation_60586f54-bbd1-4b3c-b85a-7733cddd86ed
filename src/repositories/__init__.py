"""
طبقة المستودعات
Repository layer for data access
"""

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from repositories.base_repository import BaseRepository
    from repositories.student_repository import StudentRepository
    from repositories.absence_repository import AbsenceRepository
    from repositories.report_repository import ReportRepository, ReportTemplate
except ImportError:
    try:
        from src.repositories.base_repository import BaseRepository
        from src.repositories.student_repository import StudentRepository
        from src.repositories.absence_repository import AbsenceRepository
        from src.repositories.report_repository import ReportRepository, ReportTemplate
    except ImportError:
        # Fallback for relative imports
        from .base_repository import BaseRepository
        from .student_repository import StudentRepository
        from .absence_repository import AbsenceRepository
        from .report_repository import ReportRepository, ReportTemplate

__all__ = [
    'BaseRepository',
    'StudentRepository',
    'AbsenceRepository',
    'ReportRepository',
    'ReportTemplate'
]
