"""
خدمة استيراد Excel
Excel import service for importing student data
"""

import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
import os

from .base_service import BaseService
from ..models.student import Student
from ..repositories.student_repository import StudentRepository
from ..data.database_manager import DatabaseManager
from ..utils.logger import get_logger
from ..utils.exceptions import BusinessLogicException, ValidationException, ImportException

logger = get_logger("ExcelImportService")


class ExcelImportService(BaseService):
    """
    خدمة استيراد Excel
    Excel import service
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
        self.student_repo = self.get_repository(StudentRepository)
    
    def import_students_from_excel(self, file_path: str, sheet_name: str = None, 
                                 column_mapping: Dict[str, str] = None,
                                 skip_duplicates: bool = True,
                                 validate_data: bool = True,
                                 imported_by: str = None) -> Dict[str, Any]:
        """
        استيراد الطلاب من ملف Excel
        Import students from Excel file
        """
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                raise ValidationException("ملف Excel غير موجود")
            
            # قراءة ملف Excel
            excel_data = self._read_excel_file(file_path, sheet_name)
            
            # تحديد تطابق الأعمدة
            if not column_mapping:
                column_mapping = self._auto_detect_columns(excel_data.columns.tolist())
            
            # تحويل البيانات إلى نماذج الطلاب
            students_data = self._convert_excel_to_students(excel_data, column_mapping, validate_data)
            
            # استيراد الطلاب
            import_result = self._import_students_batch(students_data, skip_duplicates, imported_by)
            
            self.log_operation(
                operation="IMPORT_STUDENTS_FROM_EXCEL",
                details=f"استيراد {import_result['successful_count']} طالب من ملف {os.path.basename(file_path)}",
                user_id=imported_by
            )
            
            return self.format_success_response(
                data=import_result,
                message=f"تم استيراد {import_result['successful_count']} طالب بنجاح",
                operation="import_students_from_excel"
            )
            
        except (ValidationException, ImportException) as e:
            raise e
        except Exception as e:
            self.handle_business_error("import_students_from_excel", e, {"file_path": file_path})
    
    def preview_excel_import(self, file_path: str, sheet_name: str = None, 
                           column_mapping: Dict[str, str] = None,
                           max_preview_rows: int = 10) -> Dict[str, Any]:
        """
        معاينة استيراد Excel
        Preview Excel import
        """
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                raise ValidationException("ملف Excel غير موجود")
            
            # قراءة ملف Excel
            excel_data = self._read_excel_file(file_path, sheet_name)
            
            # تحديد تطابق الأعمدة
            if not column_mapping:
                column_mapping = self._auto_detect_columns(excel_data.columns.tolist())
            
            # معاينة البيانات
            preview_data = excel_data.head(max_preview_rows)
            
            # تحليل البيانات
            analysis = self._analyze_excel_data(excel_data, column_mapping)
            
            return self.format_success_response(
                data={
                    "file_info": {
                        "file_name": os.path.basename(file_path),
                        "sheet_name": sheet_name or "الورقة الأولى",
                        "total_rows": len(excel_data),
                        "total_columns": len(excel_data.columns)
                    },
                    "column_mapping": column_mapping,
                    "available_columns": excel_data.columns.tolist(),
                    "preview_data": preview_data.to_dict('records'),
                    "analysis": analysis
                },
                operation="preview_excel_import"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("preview_excel_import", e, {"file_path": file_path})
    
    def validate_excel_data(self, file_path: str, sheet_name: str = None,
                          column_mapping: Dict[str, str] = None) -> Dict[str, Any]:
        """
        التحقق من صحة بيانات Excel
        Validate Excel data
        """
        try:
            # قراءة ملف Excel
            excel_data = self._read_excel_file(file_path, sheet_name)
            
            # تحديد تطابق الأعمدة
            if not column_mapping:
                column_mapping = self._auto_detect_columns(excel_data.columns.tolist())
            
            # التحقق من صحة البيانات
            validation_results = self._validate_excel_data(excel_data, column_mapping)
            
            return self.format_success_response(
                data=validation_results,
                operation="validate_excel_data"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("validate_excel_data", e, {"file_path": file_path})
    
    def get_column_mapping_suggestions(self, file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """
        الحصول على اقتراحات تطابق الأعمدة
        Get column mapping suggestions
        """
        try:
            # قراءة ملف Excel
            excel_data = self._read_excel_file(file_path, sheet_name)
            
            # تحليل الأعمدة واقتراح التطابق
            suggestions = self._suggest_column_mapping(excel_data.columns.tolist())
            
            return self.format_success_response(
                data={
                    "available_columns": excel_data.columns.tolist(),
                    "suggested_mapping": suggestions,
                    "required_fields": ["name", "civil_id", "grade_code"],
                    "optional_fields": ["phone", "parent_phone", "address", "birth_date", "notes"]
                },
                operation="get_column_mapping_suggestions"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("get_column_mapping_suggestions", e, {"file_path": file_path})
    
    def export_template(self, file_path: str, include_sample_data: bool = False) -> Dict[str, Any]:
        """
        تصدير قالب Excel للاستيراد
        Export Excel template for import
        """
        try:
            # إنشاء DataFrame للقالب
            template_columns = [
                "اسم الطالب",
                "رقم الهوية",
                "رمز الصف",
                "رقم الهاتف",
                "هاتف ولي الأمر",
                "العنوان",
                "تاريخ الميلاد",
                "ملاحظات"
            ]
            
            if include_sample_data:
                sample_data = [
                    ["أحمد محمد علي", "1234567890", "1A", "0501234567", "0509876543", "الرياض", "2010-01-15", ""],
                    ["فاطمة عبدالله", "0987654321", "1B", "0502345678", "0508765432", "جدة", "2010-03-20", ""],
                    ["محمد سعد", "1122334455", "2A", "0503456789", "0507654321", "الدمام", "2009-05-10", ""]
                ]
                df = pd.DataFrame(sample_data, columns=template_columns)
            else:
                df = pd.DataFrame(columns=template_columns)
            
            # حفظ القالب
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            self.log_operation(
                operation="EXPORT_EXCEL_TEMPLATE",
                details=f"تصدير قالب Excel إلى {file_path}"
            )
            
            return self.format_success_response(
                data={
                    "template_path": file_path,
                    "columns": template_columns,
                    "sample_data_included": include_sample_data
                },
                message="تم تصدير قالب Excel بنجاح",
                operation="export_template"
            )
            
        except Exception as e:
            self.handle_business_error("export_template", e, {"file_path": file_path})
    
    def _read_excel_file(self, file_path: str, sheet_name: str = None) -> pd.DataFrame:
        """قراءة ملف Excel"""
        try:
            # محاولة قراءة الملف بصيغ مختلفة
            try:
                # محاولة قراءة كملف Excel حديث
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            except:
                try:
                    # محاولة قراءة كملف Excel قديم
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
                except:
                    # محاولة قراءة كملف CSV
                    df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                raise ImportException("ملف Excel فارغ")
            
            # تنظيف البيانات
            df = df.dropna(how='all')  # حذف الصفوف الفارغة تماماً
            df = df.fillna('')  # استبدال القيم المفقودة بنص فارغ
            
            logger.info(f"تم قراءة ملف Excel بنجاح: {len(df)} صف")
            return df
            
        except Exception as e:
            logger.error(f"خطأ في قراءة ملف Excel: {e}")
            raise ImportException(f"فشل في قراءة ملف Excel: {e}")
    
    def _auto_detect_columns(self, columns: List[str]) -> Dict[str, str]:
        """الكشف التلقائي عن الأعمدة"""
        mapping = {}
        
        # قواميس للبحث عن الأعمدة
        name_keywords = ["اسم", "name", "student", "طالب"]
        civil_id_keywords = ["هوية", "civil", "id", "رقم", "هوية"]
        grade_keywords = ["صف", "grade", "class", "فصل"]
        phone_keywords = ["هاتف", "phone", "جوال", "mobile"]
        parent_phone_keywords = ["ولي", "parent", "والد", "أب"]
        address_keywords = ["عنوان", "address", "سكن"]
        birth_date_keywords = ["ميلاد", "birth", "تاريخ"]
        notes_keywords = ["ملاحظات", "notes", "تعليق"]
        
        for col in columns:
            col_lower = col.lower()
            
            # البحث عن عمود الاسم
            if not mapping.get("name") and any(keyword in col_lower for keyword in name_keywords):
                mapping["name"] = col
            
            # البحث عن عمود رقم الهوية
            elif not mapping.get("civil_id") and any(keyword in col_lower for keyword in civil_id_keywords):
                mapping["civil_id"] = col
            
            # البحث عن عمود الصف
            elif not mapping.get("grade_code") and any(keyword in col_lower for keyword in grade_keywords):
                mapping["grade_code"] = col
            
            # البحث عن عمود الهاتف
            elif not mapping.get("phone") and any(keyword in col_lower for keyword in phone_keywords):
                if not any(parent_keyword in col_lower for parent_keyword in parent_phone_keywords):
                    mapping["phone"] = col
            
            # البحث عن عمود هاتف ولي الأمر
            elif not mapping.get("parent_phone") and any(keyword in col_lower for keyword in phone_keywords):
                if any(parent_keyword in col_lower for parent_keyword in parent_phone_keywords):
                    mapping["parent_phone"] = col
            
            # البحث عن عمود العنوان
            elif not mapping.get("address") and any(keyword in col_lower for keyword in address_keywords):
                mapping["address"] = col
            
            # البحث عن عمود تاريخ الميلاد
            elif not mapping.get("birth_date") and any(keyword in col_lower for keyword in birth_date_keywords):
                mapping["birth_date"] = col
            
            # البحث عن عمود الملاحظات
            elif not mapping.get("notes") and any(keyword in col_lower for keyword in notes_keywords):
                mapping["notes"] = col
        
        return mapping
    
    def _suggest_column_mapping(self, columns: List[str]) -> Dict[str, List[str]]:
        """اقتراح تطابق الأعمدة"""
        suggestions = {}
        
        # الحقول المطلوبة
        required_fields = {
            "name": ["اسم", "name", "student", "طالب"],
            "civil_id": ["هوية", "civil", "id", "رقم"],
            "grade_code": ["صف", "grade", "class", "فصل"]
        }
        
        # الحقول الاختيارية
        optional_fields = {
            "phone": ["هاتف", "phone", "جوال", "mobile"],
            "parent_phone": ["ولي", "parent", "والد"],
            "address": ["عنوان", "address", "سكن"],
            "birth_date": ["ميلاد", "birth", "تاريخ"],
            "notes": ["ملاحظات", "notes", "تعليق"]
        }
        
        all_fields = {**required_fields, **optional_fields}
        
        for field, keywords in all_fields.items():
            suggestions[field] = []
            for col in columns:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in keywords):
                    suggestions[field].append(col)
        
        return suggestions
    
    def _convert_excel_to_students(self, excel_data: pd.DataFrame, 
                                 column_mapping: Dict[str, str], 
                                 validate_data: bool = True) -> List[Student]:
        """تحويل بيانات Excel إلى نماذج الطلاب"""
        students = []
        errors = []
        
        for index, row in excel_data.iterrows():
            try:
                student = Student()
                
                # تطبيق تطابق الأعمدة
                for field, column in column_mapping.items():
                    if column in row:
                        value = str(row[column]).strip() if pd.notna(row[column]) else ""
                        setattr(student, field, value)
                
                # التحقق من الحقول المطلوبة
                if not student.name:
                    errors.append(f"الصف {index + 2}: اسم الطالب مطلوب")
                    continue
                
                if not student.civil_id:
                    errors.append(f"الصف {index + 2}: رقم الهوية مطلوب")
                    continue
                
                if not student.grade_code:
                    errors.append(f"الصف {index + 2}: رمز الصف مطلوب")
                    continue
                
                # التحقق من صحة البيانات
                if validate_data:
                    validation_errors = student.validate()
                    if validation_errors:
                        errors.append(f"الصف {index + 2}: {', '.join(validation_errors)}")
                        continue
                
                students.append(student)
                
            except Exception as e:
                errors.append(f"الصف {index + 2}: خطأ في معالجة البيانات - {str(e)}")
        
        if errors:
            logger.warning(f"أخطاء في تحويل البيانات: {len(errors)} خطأ")
            for error in errors[:10]:  # عرض أول 10 أخطاء فقط
                logger.warning(error)
        
        return students
    
    def _import_students_batch(self, students: List[Student], skip_duplicates: bool = True, 
                             imported_by: str = None) -> Dict[str, Any]:
        """استيراد مجموعة من الطلاب"""
        successful_imports = []
        failed_imports = []
        skipped_duplicates = []
        
        for student in students:
            try:
                # التحقق من التكرار
                if skip_duplicates:
                    existing_student = self.student_repo.get_by_civil_id(student.civil_id)
                    if existing_student:
                        skipped_duplicates.append({
                            "student": student.to_dict(),
                            "reason": "رقم الهوية موجود مسبقاً"
                        })
                        continue
                
                # إدراج الطالب
                if self.student_repo.insert(student):
                    successful_imports.append(student.to_dict())
                else:
                    failed_imports.append({
                        "student": student.to_dict(),
                        "reason": "فشل في حفظ البيانات"
                    })
                    
            except Exception as e:
                failed_imports.append({
                    "student": student.to_dict(),
                    "reason": str(e)
                })
        
        return {
            "successful_imports": successful_imports,
            "failed_imports": failed_imports,
            "skipped_duplicates": skipped_duplicates,
            "successful_count": len(successful_imports),
            "failed_count": len(failed_imports),
            "skipped_count": len(skipped_duplicates),
            "total_processed": len(students)
        }
    
    def _analyze_excel_data(self, excel_data: pd.DataFrame, column_mapping: Dict[str, str]) -> Dict[str, Any]:
        """تحليل بيانات Excel"""
        analysis = {
            "total_rows": len(excel_data),
            "empty_rows": excel_data.isnull().all(axis=1).sum(),
            "columns_analysis": {},
            "data_quality": {}
        }
        
        # تحليل الأعمدة
        for field, column in column_mapping.items():
            if column in excel_data.columns:
                col_data = excel_data[column]
                analysis["columns_analysis"][field] = {
                    "column_name": column,
                    "non_empty_count": col_data.notna().sum(),
                    "empty_count": col_data.isna().sum(),
                    "unique_values": col_data.nunique(),
                    "sample_values": col_data.dropna().head(5).tolist()
                }
        
        # تحليل جودة البيانات
        if "civil_id" in column_mapping and column_mapping["civil_id"] in excel_data.columns:
            civil_ids = excel_data[column_mapping["civil_id"]].dropna()
            analysis["data_quality"]["duplicate_civil_ids"] = len(civil_ids) - len(civil_ids.unique())
        
        return analysis
    
    def _validate_excel_data(self, excel_data: pd.DataFrame, column_mapping: Dict[str, str]) -> Dict[str, Any]:
        """التحقق من صحة بيانات Excel"""
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "summary": {}
        }
        
        # التحقق من وجود الحقول المطلوبة
        required_fields = ["name", "civil_id", "grade_code"]
        for field in required_fields:
            if field not in column_mapping:
                validation_results["errors"].append(f"الحقل المطلوب '{field}' غير محدد")
                validation_results["is_valid"] = False
        
        # التحقق من البيانات
        for index, row in excel_data.iterrows():
            row_errors = []
            
            # التحقق من الاسم
            if "name" in column_mapping:
                name = str(row.get(column_mapping["name"], "")).strip()
                if not name or name == "nan":
                    row_errors.append("الاسم مطلوب")
            
            # التحقق من رقم الهوية
            if "civil_id" in column_mapping:
                civil_id = str(row.get(column_mapping["civil_id"], "")).strip()
                if not civil_id or civil_id == "nan":
                    row_errors.append("رقم الهوية مطلوب")
                elif len(civil_id) != 10 or not civil_id.isdigit():
                    row_errors.append("رقم الهوية يجب أن يكون 10 أرقام")
            
            if row_errors:
                validation_results["errors"].extend([f"الصف {index + 2}: {error}" for error in row_errors])
                validation_results["is_valid"] = False
        
        # ملخص التحقق
        validation_results["summary"] = {
            "total_rows": len(excel_data),
            "error_count": len(validation_results["errors"]),
            "warning_count": len(validation_results["warnings"])
        }
        
        return validation_results
