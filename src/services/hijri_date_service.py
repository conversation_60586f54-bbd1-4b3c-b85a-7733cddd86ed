"""
خدمة التاريخ الهجري
Hijri date service for date conversion and formatting
"""

from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, date

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from services.base_service import BaseService
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import BusinessLogicException, ValidationException
except ImportError:
    try:
        from src.services.base_service import BaseService
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import BusinessLogicException, ValidationException
    except ImportError:
        # Fallback for relative imports
        from .base_service import BaseService
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import BusinessLogicException, ValidationException

logger = get_logger("HijriDateService")


class HijriDateService(BaseService):
    """
    خدمة التاريخ الهجري
    Hijri date service
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
        self._hijri_converter = None
        self._initialize_hijri_converter()
    
    def _initialize_hijri_converter(self):
        """تهيئة محول التاريخ الهجري"""
        try:
            from hijri_converter import Hijri, Gregorian
            self._hijri_converter = {
                'Hijri': Hijri,
                'Gregorian': Gregorian
            }
            logger.info("تم تهيئة محول التاريخ الهجري بنجاح")
        except ImportError:
            logger.warning("مكتبة hijri-converter غير متوفرة، سيتم استخدام التحويل الأساسي")
            self._hijri_converter = None
    
    def gregorian_to_hijri(self, gregorian_date: date) -> Dict[str, Any]:
        """
        تحويل التاريخ الميلادي إلى هجري
        Convert Gregorian date to Hijri
        """
        try:
            if self._hijri_converter:
                # استخدام مكتبة hijri-converter
                gregorian = self._hijri_converter['Gregorian'](
                    gregorian_date.year,
                    gregorian_date.month,
                    gregorian_date.day
                )
                hijri = gregorian.to_hijri()
                
                return {
                    "hijri_year": hijri.year,
                    "hijri_month": hijri.month,
                    "hijri_day": hijri.day,
                    "hijri_month_name": self._get_hijri_month_name(hijri.month),
                    "formatted_hijri": f"{hijri.day:02d}/{hijri.month:02d}/{hijri.year}",
                    "formatted_hijri_ar": f"{hijri.day} {self._get_hijri_month_name(hijri.month)} {hijri.year}هـ",
                    "gregorian_date": gregorian_date.isoformat()
                }
            else:
                # استخدام التحويل الأساسي (تقريبي)
                hijri_date = self._basic_gregorian_to_hijri(gregorian_date)
                return hijri_date
                
        except Exception as e:
            logger.error(f"خطأ في تحويل التاريخ الميلادي إلى هجري: {e}")
            raise BusinessLogicException(f"فشل في تحويل التاريخ: {e}")
    
    def hijri_to_gregorian(self, hijri_year: int, hijri_month: int, hijri_day: int) -> Dict[str, Any]:
        """
        تحويل التاريخ الهجري إلى ميلادي
        Convert Hijri date to Gregorian
        """
        try:
            if self._hijri_converter:
                # استخدام مكتبة hijri-converter
                hijri = self._hijri_converter['Hijri'](hijri_year, hijri_month, hijri_day)
                gregorian = hijri.to_gregorian()
                
                gregorian_date = date(gregorian.year, gregorian.month, gregorian.day)
                
                return {
                    "gregorian_date": gregorian_date.isoformat(),
                    "gregorian_year": gregorian.year,
                    "gregorian_month": gregorian.month,
                    "gregorian_day": gregorian.day,
                    "gregorian_month_name": self._get_gregorian_month_name(gregorian.month),
                    "formatted_gregorian": f"{gregorian.day:02d}/{gregorian.month:02d}/{gregorian.year}",
                    "formatted_gregorian_ar": f"{gregorian.day} {self._get_gregorian_month_name(gregorian.month)} {gregorian.year}م",
                    "hijri_date": f"{hijri_day:02d}/{hijri_month:02d}/{hijri_year}"
                }
            else:
                # استخدام التحويل الأساسي (تقريبي)
                gregorian_date = self._basic_hijri_to_gregorian(hijri_year, hijri_month, hijri_day)
                return gregorian_date
                
        except Exception as e:
            logger.error(f"خطأ في تحويل التاريخ الهجري إلى ميلادي: {e}")
            raise BusinessLogicException(f"فشل في تحويل التاريخ: {e}")
    
    def get_current_hijri_date(self) -> Dict[str, Any]:
        """
        الحصول على التاريخ الهجري الحالي
        Get current Hijri date
        """
        try:
            current_date = date.today()
            return self.gregorian_to_hijri(current_date)
        except Exception as e:
            logger.error(f"خطأ في الحصول على التاريخ الهجري الحالي: {e}")
            raise BusinessLogicException(f"فشل في الحصول على التاريخ الهجري: {e}")
    
    def format_date_dual(self, gregorian_date: date, format_type: str = "FULL") -> str:
        """
        تنسيق التاريخ بالميلادي والهجري معاً
        Format date with both Gregorian and Hijri
        """
        try:
            hijri_data = self.gregorian_to_hijri(gregorian_date)
            
            if format_type == "FULL":
                return f"{gregorian_date.strftime('%d/%m/%Y')}م - {hijri_data['formatted_hijri']}هـ"
            elif format_type == "ARABIC":
                gregorian_month = self._get_gregorian_month_name(gregorian_date.month)
                return f"{gregorian_date.day} {gregorian_month} {gregorian_date.year}م - {hijri_data['formatted_hijri_ar']}"
            elif format_type == "SHORT":
                return f"{gregorian_date.strftime('%d/%m/%Y')} - {hijri_data['formatted_hijri']}"
            else:
                return f"{gregorian_date.isoformat()} - {hijri_data['formatted_hijri']}"
                
        except Exception as e:
            logger.error(f"خطأ في تنسيق التاريخ المزدوج: {e}")
            return gregorian_date.strftime('%d/%m/%Y')
    
    def get_hijri_month_name(self, month_number: int) -> str:
        """
        الحصول على اسم الشهر الهجري
        Get Hijri month name
        """
        return self._get_hijri_month_name(month_number)
    
    def get_gregorian_month_name(self, month_number: int) -> str:
        """
        الحصول على اسم الشهر الميلادي
        Get Gregorian month name
        """
        return self._get_gregorian_month_name(month_number)
    
    def validate_hijri_date(self, hijri_year: int, hijri_month: int, hijri_day: int) -> bool:
        """
        التحقق من صحة التاريخ الهجري
        Validate Hijri date
        """
        try:
            # التحقق من النطاقات الأساسية
            if hijri_year < 1 or hijri_year > 2000:
                return False
            
            if hijri_month < 1 or hijri_month > 12:
                return False
            
            if hijri_day < 1 or hijri_day > 30:
                return False
            
            # التحقق المتقدم باستخدام المكتبة
            if self._hijri_converter:
                try:
                    hijri = self._hijri_converter['Hijri'](hijri_year, hijri_month, hijri_day)
                    # محاولة التحويل للتأكد من صحة التاريخ
                    hijri.to_gregorian()
                    return True
                except:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة التاريخ الهجري: {e}")
            return False
    
    def get_hijri_year_info(self, hijri_year: int) -> Dict[str, Any]:
        """
        الحصول على معلومات السنة الهجرية
        Get Hijri year information
        """
        try:
            # حساب بداية ونهاية السنة الهجرية
            year_start_hijri = (hijri_year, 1, 1)
            year_end_hijri = (hijri_year, 12, 29)  # الشهر الهجري قد يكون 29 أو 30 يوم
            
            if self._hijri_converter:
                start_gregorian = self.hijri_to_gregorian(*year_start_hijri)
                end_gregorian = self.hijri_to_gregorian(*year_end_hijri)
            else:
                start_gregorian = self._basic_hijri_to_gregorian(*year_start_hijri)
                end_gregorian = self._basic_hijri_to_gregorian(*year_end_hijri)
            
            return {
                "hijri_year": hijri_year,
                "start_date_gregorian": start_gregorian.get("gregorian_date"),
                "end_date_gregorian": end_gregorian.get("gregorian_date"),
                "is_current_year": hijri_year == self.get_current_hijri_date()["hijri_year"]
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات السنة الهجرية: {e}")
            raise BusinessLogicException(f"فشل في الحصول على معلومات السنة: {e}")
    
    def _get_hijri_month_name(self, month_number: int) -> str:
        """الحصول على اسم الشهر الهجري"""
        hijri_months = {
            1: "محرم",
            2: "صفر",
            3: "ربيع الأول",
            4: "ربيع الثاني",
            5: "جمادى الأولى",
            6: "جمادى الثانية",
            7: "رجب",
            8: "شعبان",
            9: "رمضان",
            10: "شوال",
            11: "ذو القعدة",
            12: "ذو الحجة"
        }
        return hijri_months.get(month_number, f"شهر {month_number}")
    
    def _get_gregorian_month_name(self, month_number: int) -> str:
        """الحصول على اسم الشهر الميلادي"""
        gregorian_months = {
            1: "يناير",
            2: "فبراير",
            3: "مارس",
            4: "أبريل",
            5: "مايو",
            6: "يونيو",
            7: "يوليو",
            8: "أغسطس",
            9: "سبتمبر",
            10: "أكتوبر",
            11: "نوفمبر",
            12: "ديسمبر"
        }
        return gregorian_months.get(month_number, f"شهر {month_number}")
    
    def _basic_gregorian_to_hijri(self, gregorian_date: date) -> Dict[str, Any]:
        """تحويل أساسي من الميلادي إلى الهجري (تقريبي)"""
        # تحويل تقريبي بناءً على الفرق بين التقويمين
        # هذا تحويل تقريبي وليس دقيق
        
        # الفرق التقريبي بين التقويمين
        hijri_epoch = date(622, 7, 16)  # بداية التقويم الهجري تقريباً
        
        days_diff = (gregorian_date - hijri_epoch).days
        hijri_year = int(days_diff / 354.37) + 1  # السنة الهجرية تقريباً 354.37 يوم
        
        # حساب تقريبي للشهر واليوم
        days_in_year = days_diff % 354.37
        hijri_month = int(days_in_year / 29.53) + 1  # الشهر الهجري تقريباً 29.53 يوم
        hijri_day = int(days_in_year % 29.53) + 1
        
        # تصحيح القيم
        if hijri_month > 12:
            hijri_month = 12
        if hijri_day > 30:
            hijri_day = 30
        if hijri_day < 1:
            hijri_day = 1
        
        return {
            "hijri_year": hijri_year,
            "hijri_month": hijri_month,
            "hijri_day": hijri_day,
            "hijri_month_name": self._get_hijri_month_name(hijri_month),
            "formatted_hijri": f"{hijri_day:02d}/{hijri_month:02d}/{hijri_year}",
            "formatted_hijri_ar": f"{hijri_day} {self._get_hijri_month_name(hijri_month)} {hijri_year}هـ",
            "gregorian_date": gregorian_date.isoformat(),
            "note": "تحويل تقريبي - يُنصح بتثبيت مكتبة hijri-converter للحصول على دقة أفضل"
        }
    
    def _basic_hijri_to_gregorian(self, hijri_year: int, hijri_month: int, hijri_day: int) -> Dict[str, Any]:
        """تحويل أساسي من الهجري إلى الميلادي (تقريبي)"""
        # تحويل تقريبي بناءً على الفرق بين التقويمين
        
        # حساب عدد الأيام من بداية التقويم الهجري
        total_days = (hijri_year - 1) * 354.37 + (hijri_month - 1) * 29.53 + hijri_day - 1
        
        # إضافة الأيام إلى بداية التقويم الهجري
        hijri_epoch = date(622, 7, 16)
        from datetime import timedelta
        gregorian_date = hijri_epoch + timedelta(days=int(total_days))
        
        return {
            "gregorian_date": gregorian_date.isoformat(),
            "gregorian_year": gregorian_date.year,
            "gregorian_month": gregorian_date.month,
            "gregorian_day": gregorian_date.day,
            "gregorian_month_name": self._get_gregorian_month_name(gregorian_date.month),
            "formatted_gregorian": f"{gregorian_date.day:02d}/{gregorian_date.month:02d}/{gregorian_date.year}",
            "formatted_gregorian_ar": f"{gregorian_date.day} {self._get_gregorian_month_name(gregorian_date.month)} {gregorian_date.year}م",
            "hijri_date": f"{hijri_day:02d}/{hijri_month:02d}/{hijri_year}",
            "note": "تحويل تقريبي - يُنصح بتثبيت مكتبة hijri-converter للحصول على دقة أفضل"
        }
