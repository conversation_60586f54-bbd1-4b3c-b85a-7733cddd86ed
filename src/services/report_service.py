"""
خدمة التقارير
Report service for generating and managing reports
"""

import json
from typing import Any, Dict, List, Optional
from datetime import datetime, date, timedelta
from io import BytesIO

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from services.base_service import BaseService
    from repositories.report_repository import ReportRepository, ReportTemplate
    from repositories.absence_repository import AbsenceRepository
    from repositories.student_repository import StudentRepository
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import BusinessLogicException, ValidationException, ReportException
except ImportError:
    try:
        from src.services.base_service import BaseService
        from src.repositories.report_repository import ReportRepository, ReportTemplate
        from src.repositories.absence_repository import AbsenceRepository
        from src.repositories.student_repository import StudentRepository
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import BusinessLogicException, ValidationException, ReportException
    except ImportError:
        # Fallback for relative imports
        from .base_service import BaseService
        from ..repositories.report_repository import ReportRepository, ReportTemplate
        from ..repositories.absence_repository import AbsenceRepository
        from ..repositories.student_repository import StudentRepository
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import BusinessLogicException, ValidationException, ReportException

logger = get_logger("ReportService")


class ReportService(BaseService):
    """
    خدمة التقارير
    Report service
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
        self.report_repo = self.get_repository(ReportRepository)
        self.absence_repo = self.get_repository(AbsenceRepository)
        self.student_repo = self.get_repository(StudentRepository)
    
    def generate_daily_report(self, report_date: date, grade_code: str = None, template_id: str = None) -> Dict[str, Any]:
        """
        إنشاء تقرير يومي
        Generate daily report
        """
        try:
            # الحصول على القالب
            template = self._get_report_template(template_id, "DAILY")
            
            # جمع البيانات
            report_data = self._collect_daily_report_data(report_date, grade_code)
            
            # إنشاء التقرير
            report_content = self._generate_report_content(template, report_data)
            
            self.log_operation(
                operation="GENERATE_DAILY_REPORT",
                details=f"إنشاء تقرير يومي لتاريخ {report_date}"
            )
            
            return self.format_success_response(
                data={
                    "report_type": "DAILY",
                    "date": report_date.isoformat(),
                    "grade_code": grade_code,
                    "template": template.to_dict(),
                    "content": report_content,
                    "generated_at": datetime.now().isoformat()
                },
                message="تم إنشاء التقرير اليومي بنجاح",
                operation="generate_daily_report"
            )
            
        except Exception as e:
            self.handle_business_error("generate_daily_report", e, {
                "date": str(report_date),
                "grade_code": grade_code
            })
    
    def generate_weekly_report(self, start_date: date, end_date: date, grade_code: str = None, template_id: str = None) -> Dict[str, Any]:
        """
        إنشاء تقرير أسبوعي
        Generate weekly report
        """
        try:
            # التحقق من نطاق التاريخ
            self.validate_date_range(start_date, end_date)
            
            # الحصول على القالب
            template = self._get_report_template(template_id, "WEEKLY")
            
            # جمع البيانات
            report_data = self._collect_weekly_report_data(start_date, end_date, grade_code)
            
            # إنشاء التقرير
            report_content = self._generate_report_content(template, report_data)
            
            self.log_operation(
                operation="GENERATE_WEEKLY_REPORT",
                details=f"إنشاء تقرير أسبوعي من {start_date} إلى {end_date}"
            )
            
            return self.format_success_response(
                data={
                    "report_type": "WEEKLY",
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "grade_code": grade_code,
                    "template": template.to_dict(),
                    "content": report_content,
                    "generated_at": datetime.now().isoformat()
                },
                message="تم إنشاء التقرير الأسبوعي بنجاح",
                operation="generate_weekly_report"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("generate_weekly_report", e, {
                "start_date": str(start_date),
                "end_date": str(end_date),
                "grade_code": grade_code
            })
    
    def generate_monthly_report(self, year: int, month: int, grade_code: str = None, template_id: str = None) -> Dict[str, Any]:
        """
        إنشاء تقرير شهري
        Generate monthly report
        """
        try:
            # حساب تواريخ بداية ونهاية الشهر
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
            
            # الحصول على القالب
            template = self._get_report_template(template_id, "MONTHLY")
            
            # جمع البيانات
            report_data = self._collect_monthly_report_data(start_date, end_date, grade_code)
            
            # إنشاء التقرير
            report_content = self._generate_report_content(template, report_data)
            
            self.log_operation(
                operation="GENERATE_MONTHLY_REPORT",
                details=f"إنشاء تقرير شهري لشهر {month}/{year}"
            )
            
            return self.format_success_response(
                data={
                    "report_type": "MONTHLY",
                    "year": year,
                    "month": month,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "grade_code": grade_code,
                    "template": template.to_dict(),
                    "content": report_content,
                    "generated_at": datetime.now().isoformat()
                },
                message="تم إنشاء التقرير الشهري بنجاح",
                operation="generate_monthly_report"
            )
            
        except Exception as e:
            self.handle_business_error("generate_monthly_report", e, {
                "year": year,
                "month": month,
                "grade_code": grade_code
            })
    
    def export_report_to_word(self, report_data: Dict[str, Any]) -> BytesIO:
        """
        تصدير التقرير إلى ملف Word
        Export report to Word document
        """
        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            
            # إنشاء مستند جديد
            doc = Document()
            
            # إعداد اتجاه النص للعربية
            sections = doc.sections
            for section in sections:
                section.page_height = Inches(11.69)  # A4
                section.page_width = Inches(8.27)
            
            # إضافة عنوان التقرير
            title = doc.add_heading(report_data.get("title", "تقرير الغياب"), 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # إضافة معلومات التقرير
            info_table = doc.add_table(rows=0, cols=2)
            info_table.style = 'Table Grid'
            
            # إضافة بيانات التقرير
            report_info = [
                ("نوع التقرير", report_data.get("report_type", "")),
                ("التاريخ", report_data.get("date", "")),
                ("الصف", report_data.get("grade_code", "جميع الصفوف")),
                ("تاريخ الإنشاء", datetime.now().strftime("%Y-%m-%d %H:%M"))
            ]
            
            for key, value in report_info:
                row = info_table.add_row()
                row.cells[0].text = key
                row.cells[1].text = str(value)
            
            doc.add_paragraph()
            
            # إضافة محتوى التقرير
            content = report_data.get("content", {})
            
            # إضافة الإحصائيات
            if "statistics" in content:
                stats = content["statistics"]
                doc.add_heading("الإحصائيات", level=1)
                
                stats_table = doc.add_table(rows=0, cols=2)
                stats_table.style = 'Table Grid'
                
                stats_data = [
                    ("إجمالي الغيابات", stats.get("total_absences", 0)),
                    ("الغيابات المبررة", stats.get("excused_absences", 0)),
                    ("الغيابات غير المبررة", stats.get("unexcused_absences", 0)),
                    ("عدد الطلاب الغائبين", stats.get("students_with_absences", 0))
                ]
                
                for key, value in stats_data:
                    row = stats_table.add_row()
                    row.cells[0].text = key
                    row.cells[1].text = str(value)
            
            # إضافة جدول الغيابات
            if "absences" in content and content["absences"]:
                doc.add_heading("تفاصيل الغيابات", level=1)
                
                absence_table = doc.add_table(rows=1, cols=6)
                absence_table.style = 'Table Grid'
                
                # رؤوس الأعمدة
                headers = ["اسم الطالب", "رقم الهوية", "التاريخ", "النوع", "السبب", "الملاحظات"]
                for i, header in enumerate(headers):
                    absence_table.rows[0].cells[i].text = header
                
                # بيانات الغيابات
                for absence in content["absences"]:
                    row = absence_table.add_row()
                    row.cells[0].text = absence.get("student_name", "")
                    row.cells[1].text = absence.get("student_civil_id", "")
                    row.cells[2].text = absence.get("absence_date", "")
                    row.cells[3].text = "مبرر" if absence.get("absence_type") == "EXCUSED" else "غير مبرر"
                    row.cells[4].text = absence.get("reason", "")
                    row.cells[5].text = absence.get("notes", "")
            
            # حفظ المستند في الذاكرة
            doc_buffer = BytesIO()
            doc.save(doc_buffer)
            doc_buffer.seek(0)
            
            self.log_operation(
                operation="EXPORT_REPORT_TO_WORD",
                details="تصدير تقرير إلى ملف Word"
            )
            
            return doc_buffer
            
        except Exception as e:
            logger.error(f"خطأ في تصدير التقرير إلى Word: {e}")
            raise ReportException(f"فشل في تصدير التقرير: {e}")
    
    def create_custom_template(self, name: str, description: str, template_type: str, 
                             template_content: str, created_by: str = None) -> Dict[str, Any]:
        """
        إنشاء قالب تقرير مخصص
        Create custom report template
        """
        try:
            # التحقق من صحة البيانات
            self._validate_template_data(name, template_type, template_content)
            
            # إنشاء القالب
            template = ReportTemplate()
            template.name = name
            template.description = description
            template.template_type = template_type
            template.template_content = template_content
            template.is_default = False
            
            # حفظ القالب
            if self.report_repo.insert(template):
                self.log_operation(
                    operation="CREATE_CUSTOM_TEMPLATE",
                    details=f"إنشاء قالب مخصص: {name}",
                    user_id=created_by
                )
                
                return self.format_success_response(
                    data=template.to_dict(),
                    message="تم إنشاء القالب المخصص بنجاح",
                    operation="create_custom_template"
                )
            else:
                raise BusinessLogicException("فشل في حفظ القالب المخصص")
                
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("create_custom_template", e, {"name": name, "type": template_type})
    
    def get_available_templates(self, template_type: str = None) -> Dict[str, Any]:
        """
        الحصول على القوالب المتاحة
        Get available templates
        """
        try:
            if template_type:
                templates = self.report_repo.get_by_type(template_type)
            else:
                templates = self.report_repo.get_all()
            
            # تجميع القوالب حسب النوع
            templates_by_type = {}
            for template in templates:
                if template.template_type not in templates_by_type:
                    templates_by_type[template.template_type] = {
                        "default": [],
                        "custom": []
                    }
                
                if template.is_default:
                    templates_by_type[template.template_type]["default"].append(template.to_dict())
                else:
                    templates_by_type[template.template_type]["custom"].append(template.to_dict())
            
            return self.format_success_response(
                data={
                    "templates_by_type": templates_by_type,
                    "total_count": len(templates)
                },
                operation="get_available_templates"
            )
            
        except Exception as e:
            self.handle_business_error("get_available_templates", e, {"template_type": template_type})
    
    def _get_report_template(self, template_id: str = None, template_type: str = "DAILY") -> ReportTemplate:
        """الحصول على قالب التقرير"""
        if template_id:
            template = self.report_repo.get_by_id(template_id)
            if not template:
                raise ValidationException("قالب التقرير غير موجود")
        else:
            # الحصول على القالب الافتراضي
            templates = self.report_repo.get_by_type(template_type)
            default_templates = [t for t in templates if t.is_default]
            
            if default_templates:
                template = default_templates[0]
            else:
                raise ReportException(f"لا يوجد قالب افتراضي للنوع {template_type}")
        
        return template
    
    def _collect_daily_report_data(self, report_date: date, grade_code: str = None) -> Dict[str, Any]:
        """جمع بيانات التقرير اليومي"""
        # الحصول على الغيابات
        if grade_code:
            absences = self.absence_repo.get_by_grade_and_date(grade_code, report_date)
        else:
            absences = self.absence_repo.get_by_date_range(report_date, report_date)
        
        # حساب الإحصائيات
        total_absences = len(absences)
        excused_absences = sum(1 for a in absences if a.absence_type == "EXCUSED")
        unexcused_absences = total_absences - excused_absences
        
        return {
            "date": report_date.isoformat(),
            "grade_code": grade_code,
            "absences": [absence.to_dict() for absence in absences],
            "statistics": {
                "total_absences": total_absences,
                "excused_absences": excused_absences,
                "unexcused_absences": unexcused_absences
            }
        }
    
    def _collect_weekly_report_data(self, start_date: date, end_date: date, grade_code: str = None) -> Dict[str, Any]:
        """جمع بيانات التقرير الأسبوعي"""
        # الحصول على الغيابات
        absences = self.absence_repo.get_by_date_range(start_date, end_date, grade_code)
        
        # الحصول على الإحصائيات
        stats = self.absence_repo.get_absence_statistics(start_date, end_date, grade_code)
        
        return {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "grade_code": grade_code,
            "absences": [absence.to_dict() for absence in absences],
            "statistics": stats
        }
    
    def _collect_monthly_report_data(self, start_date: date, end_date: date, grade_code: str = None) -> Dict[str, Any]:
        """جمع بيانات التقرير الشهري"""
        
        # الحصول على الغيابات
        absences = self.absence_repo.get_by_date_range(start_date, end_date, grade_code)
        
        # الحصول على الإحصائيات
        stats = self.absence_repo.get_absence_statistics(start_date, end_date, grade_code)
        
        # إحصائيات إضافية للتقرير الشهري
        # تجميع الغيابات حسب الطالب
        student_absences = {}
        for absence in absences:
            student_id = absence.student_id
            if student_id not in student_absences:
                student_absences[student_id] = {
                    "student_name": getattr(absence, 'student_name', ''),
                    "total": 0,
                    "excused": 0,
                    "unexcused": 0
                }
            
            student_absences[student_id]["total"] += 1
            if absence.absence_type == "EXCUSED":
                student_absences[student_id]["excused"] += 1
            else:
                student_absences[student_id]["unexcused"] += 1
        
        return {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "grade_code": grade_code,
            "absences": [absence.to_dict() for absence in absences],
            "statistics": stats,
            "student_summary": student_absences
        }
    
    def _generate_report_content(self, template: ReportTemplate, data: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء محتوى التقرير"""
        try:
            template_content = json.loads(template.template_content)
            
            # إنشاء محتوى التقرير بناءً على القالب
            report_content = {
                "title": template_content.get("title", "تقرير الغياب"),
                "sections": template_content.get("sections", []),
                "data": data
            }
            
            return report_content
        except json.JSONDecodeError as e:
            raise ReportException(f"خطأ في تحليل قالب التقرير: {e}")
    
    def _validate_template_data(self, name: str, template_type: str, template_content: str):
        """التحقق من صحة بيانات القالب"""
        if not name or not name.strip():
            raise ValidationException("اسم القالب مطلوب")
        
        if template_type not in ["DAILY", "WEEKLY", "MONTHLY", "CUSTOM"]:
            raise ValidationException("نوع القالب غير صحيح")
        
        if not template_content or not template_content.strip():
            raise ValidationException("محتوى القالب مطلوب")
        
        # التحقق من صحة JSON
        if not self.report_repo.validate_template_content(template_content):
            raise ValidationException("محتوى القالب غير صحيح")
