"""
خدمة الغياب
Absence service for business logic operations
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, date, timedelta

import sys
from pathlib import Path

# إضافة مسارات للاستيراد
current_dir = Path(__file__).parent
src_dir = current_dir.parent
root_dir = src_dir.parent
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(root_dir))

try:
    from services.base_service import BaseService
    from models.absence_record import AbsenceRecord
    from models.student import Student
    from repositories.absence_repository import AbsenceRepository
    from repositories.student_repository import StudentRepository
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import BusinessLogicException, ValidationException, DuplicateAbsenceException
except ImportError:
    try:
        from src.services.base_service import BaseService
        from src.models.absence_record import AbsenceRecord
        from src.models.student import Student
        from src.repositories.absence_repository import AbsenceRepository
        from src.repositories.student_repository import StudentRepository
        from src.data.database_manager import DatabaseManager
        from src.utils.logger import get_logger
        from src.utils.exceptions import BusinessLogicException, ValidationException, DuplicateAbsenceException
    except ImportError:
        # Fallback for relative imports
        from .base_service import BaseService
        from ..models.absence_record import AbsenceRecord
        from ..models.student import Student
        from ..repositories.absence_repository import AbsenceRepository
        from ..repositories.student_repository import StudentRepository
        from ..data.database_manager import DatabaseManager
        from ..utils.logger import get_logger
        from ..utils.exceptions import BusinessLogicException, ValidationException, DuplicateAbsenceException

logger = get_logger("AbsenceService")


class AbsenceService(BaseService):
    """
    خدمة الغياب
    Absence service
    """
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager)
        self.absence_repo = self.get_repository(AbsenceRepository)
        self.student_repo = self.get_repository(StudentRepository)
    
    def record_absence(self, student_id: str, absence_date: date, absence_type: str = "UNEXCUSED",
                      reason: str = None, period: str = "FULL_DAY", semester: str = None,
                      academic_year: str = None, recorded_by: str = None, notes: str = None) -> Dict[str, Any]:
        """
        تسجيل غياب طالب
        Record student absence
        """
        try:
            # التحقق من صحة البيانات
            self._validate_absence_data(student_id, absence_date, absence_type, period)
            
            # التحقق من وجود الطالب
            student = self.student_repo.get_by_id(student_id)
            if not student:
                raise ValidationException("الطالب غير موجود")
            
            if not student.is_active:
                raise ValidationException("الطالب غير نشط")
            
            # التحقق من عدم تكرار الغياب
            existing_absence = self.absence_repo.get_by_student_and_date(student_id, absence_date, period)
            if existing_absence:
                raise DuplicateAbsenceException("سجل الغياب موجود مسبقاً لهذا اليوم والفترة")
            
            # إنشاء سجل الغياب
            absence = AbsenceRecord()
            absence.student_id = student_id
            absence.absence_date = absence_date
            absence.absence_type = absence_type
            absence.reason = reason or ""
            absence.period = period
            absence.semester = semester or self._get_current_semester()
            absence.academic_year = academic_year or self._get_current_academic_year()
            absence.recorded_by = recorded_by or "system"
            absence.notes = notes or ""
            
            # حفظ السجل
            if self.absence_repo.insert(absence):
                self.log_operation(
                    operation="RECORD_ABSENCE",
                    details=f"تسجيل غياب للطالب {student.name} في {absence_date}",
                    user_id=recorded_by
                )
                
                return self.format_success_response(
                    data=absence.to_dict(),
                    message="تم تسجيل الغياب بنجاح",
                    operation="record_absence"
                )
            else:
                raise BusinessLogicException("فشل في حفظ سجل الغياب")
                
        except (ValidationException, DuplicateAbsenceException) as e:
            raise e
        except Exception as e:
            self.handle_business_error("record_absence", e, {"student_id": student_id, "date": str(absence_date)})
    
    def update_absence(self, absence_id: str, absence_type: str = None, reason: str = None,
                      notes: str = None, updated_by: str = None) -> Dict[str, Any]:
        """
        تحديث سجل غياب
        Update absence record
        """
        try:
            # الحصول على السجل الحالي
            absence = self.absence_repo.get_by_id(absence_id)
            if not absence:
                raise ValidationException("سجل الغياب غير موجود")
            
            # تحديث البيانات
            if absence_type is not None:
                if absence_type not in ["EXCUSED", "UNEXCUSED"]:
                    raise ValidationException("نوع الغياب غير صحيح")
                absence.absence_type = absence_type
            
            if reason is not None:
                absence.reason = reason
            
            if notes is not None:
                absence.notes = notes
            
            # حفظ التحديثات
            if self.absence_repo.update(absence):
                self.log_operation(
                    operation="UPDATE_ABSENCE",
                    details=f"تحديث سجل غياب {absence_id}",
                    user_id=updated_by
                )
                
                return self.format_success_response(
                    data=absence.to_dict(),
                    message="تم تحديث سجل الغياب بنجاح",
                    operation="update_absence"
                )
            else:
                raise BusinessLogicException("فشل في تحديث سجل الغياب")
                
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("update_absence", e, {"absence_id": absence_id})
    
    def delete_absence(self, absence_id: str, deleted_by: str = None) -> Dict[str, Any]:
        """
        حذف سجل غياب
        Delete absence record
        """
        try:
            # التحقق من وجود السجل
            absence = self.absence_repo.get_by_id(absence_id)
            if not absence:
                raise ValidationException("سجل الغياب غير موجود")
            
            # حذف السجل
            if self.absence_repo.delete(absence_id):
                self.log_operation(
                    operation="DELETE_ABSENCE",
                    details=f"حذف سجل غياب {absence_id}",
                    user_id=deleted_by
                )
                
                return self.format_success_response(
                    message="تم حذف سجل الغياب بنجاح",
                    operation="delete_absence"
                )
            else:
                raise BusinessLogicException("فشل في حذف سجل الغياب")
                
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("delete_absence", e, {"absence_id": absence_id})
    
    def get_student_absences(self, student_id: str, start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """
        الحصول على سجلات غياب طالب
        Get student absence records
        """
        try:
            # التحقق من وجود الطالب
            student = self.student_repo.get_by_id(student_id)
            if not student:
                raise ValidationException("الطالب غير موجود")
            
            # التحقق من نطاق التاريخ
            if start_date and end_date:
                self.validate_date_range(start_date, end_date)
            
            # الحصول على السجلات
            absences = self.absence_repo.get_by_student(student_id, start_date, end_date)
            
            # حساب الإحصائيات
            stats = self._calculate_student_absence_stats(absences)
            
            return self.format_success_response(
                data={
                    "student": student.to_dict(),
                    "absences": [absence.to_dict() for absence in absences],
                    "statistics": stats
                },
                operation="get_student_absences"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("get_student_absences", e, {"student_id": student_id})
    
    def get_daily_absences(self, absence_date: date, grade_code: str = None) -> Dict[str, Any]:
        """
        الحصول على غيابات يوم معين
        Get daily absences
        """
        try:
            if grade_code:
                absences = self.absence_repo.get_by_grade_and_date(grade_code, absence_date)
            else:
                absences = self.absence_repo.get_by_date_range(absence_date, absence_date)
            
            # حساب الإحصائيات
            stats = self._calculate_daily_absence_stats(absences, absence_date)
            
            return self.format_success_response(
                data={
                    "date": absence_date.isoformat(),
                    "grade_code": grade_code,
                    "absences": [absence.to_dict() for absence in absences],
                    "statistics": stats
                },
                operation="get_daily_absences"
            )
            
        except Exception as e:
            self.handle_business_error("get_daily_absences", e, {"date": str(absence_date), "grade": grade_code})
    
    def get_absence_statistics(self, start_date: date, end_date: date, grade_code: str = None) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الغياب
        Get absence statistics
        """
        try:
            self.validate_date_range(start_date, end_date)
            
            # الحصول على الإحصائيات من المستودع
            stats = self.absence_repo.get_absence_statistics(start_date, end_date, grade_code)
            
            # إضافة إحصائيات إضافية
            enhanced_stats = self._enhance_absence_statistics(stats, start_date, end_date, grade_code)
            
            return self.format_success_response(
                data=enhanced_stats,
                operation="get_absence_statistics"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("get_absence_statistics", e, {
                "start_date": str(start_date),
                "end_date": str(end_date),
                "grade_code": grade_code
            })
    
    def bulk_record_absences(self, absences_data: List[Dict[str, Any]], recorded_by: str = None) -> Dict[str, Any]:
        """
        تسجيل عدة غيابات دفعة واحدة
        Bulk record absences
        """
        try:
            if not absences_data:
                raise ValidationException("لا توجد بيانات غياب للتسجيل")
            
            successful_records = []
            failed_records = []
            
            for absence_data in absences_data:
                try:
                    result = self.record_absence(
                        student_id=absence_data.get("student_id"),
                        absence_date=datetime.fromisoformat(absence_data.get("absence_date")).date(),
                        absence_type=absence_data.get("absence_type", "UNEXCUSED"),
                        reason=absence_data.get("reason"),
                        period=absence_data.get("period", "FULL_DAY"),
                        semester=absence_data.get("semester"),
                        academic_year=absence_data.get("academic_year"),
                        recorded_by=recorded_by,
                        notes=absence_data.get("notes")
                    )
                    successful_records.append(result["data"])
                except Exception as e:
                    failed_records.append({
                        "data": absence_data,
                        "error": str(e)
                    })
            
            self.log_operation(
                operation="BULK_RECORD_ABSENCES",
                details=f"تسجيل {len(successful_records)} غياب، فشل {len(failed_records)}",
                user_id=recorded_by
            )
            
            return self.format_success_response(
                data={
                    "successful": successful_records,
                    "failed": failed_records,
                    "summary": {
                        "total": len(absences_data),
                        "successful_count": len(successful_records),
                        "failed_count": len(failed_records)
                    }
                },
                message=f"تم تسجيل {len(successful_records)} غياب من أصل {len(absences_data)}",
                operation="bulk_record_absences"
            )
            
        except ValidationException as e:
            raise e
        except Exception as e:
            self.handle_business_error("bulk_record_absences", e, {"count": len(absences_data) if absences_data else 0})
    
    def _validate_absence_data(self, student_id: str, absence_date: date, absence_type: str, period: str):
        """التحقق من صحة بيانات الغياب"""
        if not student_id:
            raise ValidationException("معرف الطالب مطلوب")
        
        if not absence_date:
            raise ValidationException("تاريخ الغياب مطلوب")
        
        if absence_date > date.today():
            raise ValidationException("لا يمكن تسجيل غياب في المستقبل")
        
        if absence_type not in ["EXCUSED", "UNEXCUSED"]:
            raise ValidationException("نوع الغياب غير صحيح")
        
        if period not in ["FULL_DAY", "FIRST_PERIOD", "SECOND_PERIOD", "THIRD_PERIOD", "FOURTH_PERIOD", "FIFTH_PERIOD", "SIXTH_PERIOD"]:
            raise ValidationException("فترة الغياب غير صحيحة")
    
    def _get_current_semester(self) -> str:
        """الحصول على الفصل الدراسي الحالي"""
        # يمكن تطوير منطق أكثر تعقيداً هنا
        current_month = datetime.now().month
        if current_month >= 9 or current_month <= 1:
            return "FIRST"
        else:
            return "SECOND"
    
    def _get_current_academic_year(self) -> str:
        """الحصول على السنة الدراسية الحالية"""
        current_year = datetime.now().year
        current_month = datetime.now().month
        
        if current_month >= 9:
            return f"{current_year}-{current_year + 1}"
        else:
            return f"{current_year - 1}-{current_year}"
    
    def _calculate_student_absence_stats(self, absences: List[AbsenceRecord]) -> Dict[str, Any]:
        """حساب إحصائيات غياب الطالب"""
        total_absences = len(absences)
        excused_absences = sum(1 for a in absences if a.absence_type == "EXCUSED")
        unexcused_absences = total_absences - excused_absences
        
        return {
            "total_absences": total_absences,
            "excused_absences": excused_absences,
            "unexcused_absences": unexcused_absences,
            "excused_percentage": (excused_absences / total_absences * 100) if total_absences > 0 else 0,
            "unexcused_percentage": (unexcused_absences / total_absences * 100) if total_absences > 0 else 0
        }
    
    def _calculate_daily_absence_stats(self, absences: List[AbsenceRecord], absence_date: date) -> Dict[str, Any]:
        """حساب إحصائيات الغياب اليومي"""
        total_absences = len(absences)
        excused_absences = sum(1 for a in absences if a.absence_type == "EXCUSED")
        unexcused_absences = total_absences - excused_absences
        
        # تجميع حسب الصف
        by_grade = {}
        for absence in absences:
            grade = getattr(absence, 'grade_code', 'غير محدد')
            if grade not in by_grade:
                by_grade[grade] = {"total": 0, "excused": 0, "unexcused": 0}
            
            by_grade[grade]["total"] += 1
            if absence.absence_type == "EXCUSED":
                by_grade[grade]["excused"] += 1
            else:
                by_grade[grade]["unexcused"] += 1
        
        return {
            "date": absence_date.isoformat(),
            "total_absences": total_absences,
            "excused_absences": excused_absences,
            "unexcused_absences": unexcused_absences,
            "by_grade": by_grade
        }
    
    def _enhance_absence_statistics(self, base_stats: Dict[str, Any], start_date: date, end_date: date, grade_code: str = None) -> Dict[str, Any]:
        """تحسين إحصائيات الغياب"""
        enhanced_stats = base_stats.copy()
        
        # إضافة معلومات الفترة
        enhanced_stats["period"] = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "days_count": (end_date - start_date).days + 1,
            "grade_code": grade_code
        }
        
        # حساب المتوسطات
        days_count = enhanced_stats["period"]["days_count"]
        if days_count > 0:
            enhanced_stats["averages"] = {
                "daily_absences": enhanced_stats.get("total_absences", 0) / days_count,
                "daily_excused": enhanced_stats.get("excused_absences", 0) / days_count,
                "daily_unexcused": enhanced_stats.get("unexcused_absences", 0) / days_count
            }
        
        return enhanced_stats
