"""
الخدمة الأساسية
Base service for business logic operations
"""

from abc import ABC
from typing import Any, Dict, List, Optional

try:
    from ..data.database_manager import DatabaseManager
    from ..utils.logger import get_logger
    from ..utils.exceptions import BusinessLogicException, ValidationException
except ImportError:
    # للتشغيل المباشر للسكريبت
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from data.database_manager import DatabaseManager
    from utils.logger import get_logger
    from utils.exceptions import BusinessLogicException, ValidationException

logger = get_logger("BaseService")


class BaseService(ABC):
    """
    الخدمة الأساسية لعمليات المنطق التجاري
    Base service for business logic operations
    """
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self._repositories = {}
    
    def get_repository(self, repository_class):
        """
        الحصول على مستودع مع التخزين المؤقت
        Get repository with caching
        """
        repo_name = repository_class.__name__
        if repo_name not in self._repositories:
            self._repositories[repo_name] = repository_class(self.db_manager)
        return self._repositories[repo_name]
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> bool:
        """
        التحقق من الحقول المطلوبة
        Validate required fields
        """
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationException(f"الحقول المطلوبة مفقودة: {', '.join(missing_fields)}")
        
        return True
    
    def validate_date_range(self, start_date, end_date) -> bool:
        """
        التحقق من صحة نطاق التاريخ
        Validate date range
        """
        if start_date and end_date and start_date > end_date:
            raise ValidationException("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        return True
    
    def log_operation(self, operation: str, details: str = None, user_id: str = None):
        """
        تسجيل العملية في سجل العمليات
        Log operation to operation log
        """
        try:
            from datetime import datetime
            
            log_entry = {
                "operation": operation,
                "details": details or "",
                "user_id": user_id or "system",
                "timestamp": datetime.now().isoformat(),
                "status": "SUCCESS"
            }
            
            # يمكن إضافة تسجيل في قاعدة البيانات هنا
            logger.info(f"عملية: {operation} - {details}")
        except Exception as e:
            logger.warning(f"خطأ في تسجيل العملية: {e}")
    
    def handle_business_error(self, operation: str, error: Exception, context: Dict[str, Any] = None):
        """
        معالجة أخطاء المنطق التجاري
        Handle business logic errors
        """
        error_message = f"خطأ في {operation}: {str(error)}"
        
        if context:
            error_message += f" - السياق: {context}"
        
        logger.error(error_message)
        
        # تسجيل الخطأ في سجل العمليات
        self.log_operation(
            operation=f"ERROR_{operation}",
            details=error_message,
            user_id=context.get("user_id") if context else None
        )
        
        # رفع استثناء منطق تجاري
        raise BusinessLogicException(error_message)
    
    def execute_with_transaction(self, operation_func, *args, **kwargs):
        """
        تنفيذ عملية ضمن معاملة قاعدة بيانات
        Execute operation within database transaction
        """
        try:
            with self.db_manager.get_connection() as conn:
                # بدء المعاملة
                conn.execute("BEGIN")
                
                try:
                    # تنفيذ العملية
                    result = operation_func(*args, **kwargs)
                    
                    # تأكيد المعاملة
                    conn.commit()
                    return result
                except Exception as e:
                    # إلغاء المعاملة في حالة الخطأ
                    conn.rollback()
                    raise e
        except Exception as e:
            logger.error(f"خطأ في تنفيذ المعاملة: {e}")
            raise BusinessLogicException(f"خطأ في تنفيذ العملية: {e}")
    
    def paginate_results(self, query_func, page: int = 1, page_size: int = 50, *args, **kwargs):
        """
        تقسيم النتائج إلى صفحات
        Paginate query results
        """
        try:
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 1000:
                page_size = 50
            
            offset = (page - 1) * page_size
            
            # تنفيذ الاستعلام مع التقسيم
            results = query_func(*args, limit=page_size, offset=offset, **kwargs)
            
            # حساب العدد الإجمالي (إذا كان متاحاً)
            total_count = 0
            if hasattr(query_func, '__self__') and hasattr(query_func.__self__, 'count'):
                total_count = query_func.__self__.count()
            
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 1
            
            return {
                "data": results,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_previous": page > 1
                }
            }
        except Exception as e:
            logger.error(f"خطأ في تقسيم النتائج: {e}")
            raise BusinessLogicException(f"خطأ في تقسيم النتائج: {e}")
    
    def format_error_response(self, error: Exception, operation: str = None) -> Dict[str, Any]:
        """
        تنسيق استجابة الخطأ
        Format error response
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        response = {
            "success": False,
            "error": {
                "type": error_type,
                "message": error_message,
                "operation": operation
            }
        }
        
        # إضافة تفاصيل إضافية للأخطاء المخصصة
        if hasattr(error, 'error_code'):
            response["error"]["code"] = error.error_code
        
        if hasattr(error, 'details'):
            response["error"]["details"] = error.details
        
        return response
    
    def format_success_response(self, data: Any = None, message: str = None, operation: str = None) -> Dict[str, Any]:
        """
        تنسيق استجابة النجاح
        Format success response
        """
        response = {
            "success": True,
            "data": data
        }
        
        if message:
            response["message"] = message
        
        if operation:
            response["operation"] = operation
        
        return response
    
    def validate_permissions(self, user_id: str, operation: str, resource_id: str = None) -> bool:
        """
        التحقق من الصلاحيات
        Validate user permissions
        """
        # يمكن تطوير نظام صلاحيات متقدم هنا
        # حالياً نفترض أن جميع المستخدمين لديهم صلاحيات كاملة
        return True
    
    def cache_result(self, key: str, data: Any, ttl: int = 300):
        """
        تخزين النتيجة مؤقتاً
        Cache result temporarily
        """
        # يمكن تطوير نظام تخزين مؤقت هنا
        # حالياً نتجاهل التخزين المؤقت
        pass
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """
        الحصول على النتيجة المخزنة مؤقتاً
        Get cached result
        """
        # يمكن تطوير نظام تخزين مؤقت هنا
        # حالياً نعيد None
        return None
    
    def clear_cache(self, pattern: str = None):
        """
        مسح التخزين المؤقت
        Clear cache
        """
        # يمكن تطوير نظام تخزين مؤقت هنا
        pass
