#!/usr/bin/env python3
"""
سكريبت بسيط لاستيراد البيانات
Simple script for data import
"""

import sys
import os
import pandas as pd
import sqlite3
from pathlib import Path

# إضافة مسار src إلى sys.path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    db_path = project_root / "data" / "attendance.db"
    db_path.parent.mkdir(exist_ok=True)
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # إنشاء جدول الطلاب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            civil_id TEXT UNIQUE NOT NULL,
            grade_code TEXT NOT NULL,
            phone TEXT,
            parent_phone TEXT,
            address TEXT,
            birth_date TEXT,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول الغياب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS absences (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            absence_date DATE NOT NULL,
            absence_type TEXT DEFAULT 'غياب',
            reason TEXT,
            is_excused BOOLEAN DEFAULT 0,
            notes TEXT,
            recorded_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id),
            UNIQUE(student_id, absence_date)
        )
    ''')
    
    # إنشاء جدول قوالب التقارير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS report_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            template_type TEXT NOT NULL,
            content TEXT NOT NULL,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    
    print(f"✓ تم إنشاء قاعدة البيانات: {db_path}")
    return str(db_path)

def import_students_from_excel(excel_path, db_path):
    """استيراد الطلاب من ملف Excel"""
    try:
        # قراءة ملف Excel
        print(f"قراءة ملف Excel: {excel_path}")
        
        # محاولة قراءة الملف بصيغ مختلفة
        try:
            df = pd.read_excel(excel_path, engine='openpyxl')
        except:
            try:
                df = pd.read_excel(excel_path, engine='xlrd')
            except:
                df = pd.read_csv(excel_path, encoding='utf-8')
        
        print(f"✓ تم قراءة {len(df)} صف من الملف")
        print(f"الأعمدة المتاحة: {list(df.columns)}")
        
        # تنظيف البيانات
        df = df.dropna(how='all')  # حذف الصفوف الفارغة
        df = df.fillna('')  # استبدال القيم المفقودة
        
        # تحديد تطابق الأعمدة
        column_mapping = {}
        
        # البحث عن الأعمدة المطلوبة
        for col in df.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['اسم الطالب', 'اسم', 'name']):
                column_mapping['name'] = col
            elif any(keyword in col_lower for keyword in ['رقم الهوية', 'هوية', 'civil', 'id']):
                column_mapping['civil_id'] = col
            elif any(keyword in col_lower for keyword in ['الصف', 'صف', 'grade', 'class']):
                column_mapping['grade_code'] = col
            elif any(keyword in col_lower for keyword in ['رقم الجوال', 'هاتف', 'phone', 'جوال']):
                if 'ولي' not in col_lower and 'parent' not in col_lower:
                    column_mapping['phone'] = col
            elif any(keyword in col_lower for keyword in ['جوال ولي الأمر', 'ولي', 'parent']):
                column_mapping['parent_phone'] = col
            elif any(keyword in col_lower for keyword in ['العنوان', 'عنوان', 'address']):
                column_mapping['address'] = col
        
        print(f"تطابق الأعمدة: {column_mapping}")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        successful_imports = 0
        failed_imports = 0
        skipped_duplicates = 0
        
        for index, row in df.iterrows():
            try:
                # استخراج البيانات
                name = str(row.get(column_mapping.get('name', ''), '')).strip()
                civil_id = str(row.get(column_mapping.get('civil_id', ''), '')).strip()
                grade_code = str(row.get(column_mapping.get('grade_code', ''), '')).strip()
                phone = str(row.get(column_mapping.get('phone', ''), '')).strip()
                parent_phone = str(row.get(column_mapping.get('parent_phone', ''), '')).strip()
                address = str(row.get(column_mapping.get('address', ''), '')).strip()
                
                # التحقق من البيانات المطلوبة
                if not name or name == 'nan':
                    print(f"تخطي الصف {index + 2}: اسم الطالب مطلوب")
                    failed_imports += 1
                    continue
                
                if not civil_id or civil_id == 'nan':
                    print(f"تخطي الصف {index + 2}: رقم الهوية مطلوب")
                    failed_imports += 1
                    continue
                
                if not grade_code or grade_code == 'nan':
                    print(f"تخطي الصف {index + 2}: رمز الصف مطلوب")
                    failed_imports += 1
                    continue
                
                # التحقق من التكرار
                cursor.execute("SELECT id FROM students WHERE civil_id = ?", (civil_id,))
                if cursor.fetchone():
                    print(f"تخطي الصف {index + 2}: رقم الهوية {civil_id} موجود مسبقاً")
                    skipped_duplicates += 1
                    continue
                
                # إدراج الطالب
                cursor.execute('''
                    INSERT INTO students (name, civil_id, grade_code, phone, parent_phone, address)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (name, civil_id, grade_code, phone, parent_phone, address))
                
                successful_imports += 1
                
            except Exception as e:
                print(f"خطأ في الصف {index + 2}: {e}")
                failed_imports += 1
        
        conn.commit()
        conn.close()
        
        print("\n=== نتائج الاستيراد ===")
        print(f"تم استيراد بنجاح: {successful_imports} طالب")
        print(f"فشل في الاستيراد: {failed_imports} طالب")
        print(f"تم تخطي (مكرر): {skipped_duplicates} طالب")
        print(f"إجمالي الصفوف المعالجة: {len(df)}")
        
        return successful_imports > 0
        
    except Exception as e:
        print(f"خطأ في استيراد البيانات: {e}")
        return False

def create_default_report_templates(db_path):
    """إنشاء قوالب التقارير الافتراضية"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # قالب التقرير اليومي
        daily_template = {
            "title": "تقرير الغياب اليومي",
            "header": {
                "school_name": "مدرسة أبو عبيدة المتوسطة",
                "report_type": "تقرير غياب يومي",
                "date_field": "التاريخ"
            },
            "sections": [
                {
                    "name": "summary",
                    "title": "ملخص الغياب",
                    "fields": ["total_students", "absent_students", "present_students", "absence_rate"]
                },
                {
                    "name": "absent_students",
                    "title": "الطلاب الغائبون",
                    "fields": ["name", "grade_code", "absence_type", "reason"]
                }
            ]
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO report_templates (name, template_type, content, is_default)
            VALUES (?, ?, ?, ?)
        ''', ("قالب التقرير اليومي", "daily", str(daily_template), 1))
        
        # قالب التقرير الأسبوعي
        weekly_template = {
            "title": "تقرير الغياب الأسبوعي",
            "header": {
                "school_name": "مدرسة أبو عبيدة المتوسطة",
                "report_type": "تقرير غياب أسبوعي",
                "date_range_field": "الفترة"
            },
            "sections": [
                {
                    "name": "summary",
                    "title": "ملخص الأسبوع",
                    "fields": ["total_students", "total_absences", "average_daily_absences"]
                },
                {
                    "name": "daily_breakdown",
                    "title": "تفصيل يومي",
                    "fields": ["date", "absent_count", "absence_rate"]
                }
            ]
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO report_templates (name, template_type, content, is_default)
            VALUES (?, ?, ?, ?)
        ''', ("قالب التقرير الأسبوعي", "weekly", str(weekly_template), 1))
        
        conn.commit()
        conn.close()
        
        print("✓ تم إنشاء قوالب التقارير الافتراضية")
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء قوالب التقارير: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=== استيراد البيانات الموجودة ===")
    
    # إنشاء قاعدة البيانات
    db_path = create_database()
    
    # البحث عن ملف البيانات
    excel_file = project_root / "StudentData.xlsx"
    
    if not excel_file.exists():
        print(f"✗ ملف البيانات غير موجود: {excel_file}")
        return False
    
    print(f"✓ تم العثور على ملف البيانات: {excel_file}")
    
    # استيراد البيانات
    if import_students_from_excel(str(excel_file), db_path):
        print("✓ تم استيراد البيانات بنجاح")
        
        # إنشاء قوالب التقارير
        create_default_report_templates(db_path)
        
        # عرض إحصائيات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM students")
        total_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT grade_code, COUNT(*) FROM students GROUP BY grade_code")
        grade_stats = cursor.fetchall()
        
        conn.close()
        
        print(f"\n=== الإحصائيات النهائية ===")
        print(f"إجمالي الطلاب: {total_students}")
        print("توزيع الطلاب حسب الصف:")
        for grade, count in grade_stats:
            print(f"  {grade}: {count} طالب")
        
        return True
    else:
        print("✗ فشل في استيراد البيانات")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
